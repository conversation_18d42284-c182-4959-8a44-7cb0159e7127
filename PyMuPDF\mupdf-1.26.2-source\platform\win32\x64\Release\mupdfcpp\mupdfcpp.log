﻿  classes.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/classes.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7339,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7345,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,189): warning C4100: “arg_8”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,164): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,151): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,131): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,107): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,88): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7351,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,212): warning C4100: “arg_8”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,187): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,174): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,154): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,130): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,111): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,79): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7357,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7363,124): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7363,107): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7363,88): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7363,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7363,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7369,152): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7369,135): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7369,116): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7369,84): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7369,60): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,189): warning C4100: “arg_8”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,178): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,153): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,140): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,120): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7375,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,212): warning C4100: “arg_8”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,187): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,174): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,154): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,130): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,111): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,79): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7381,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7387,113): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7387,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7387,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7387,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7393,152): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7393,135): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7393,116): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7393,84): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7393,60): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7399,98): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7399,79): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7399,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7405,130): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7405,105): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7405,92): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7405,73): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7405,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7411,130): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7411,105): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7411,92): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7411,73): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7411,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,179): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,154): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,141): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,121): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,97): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7417,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7423,114): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7423,97): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7423,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7423,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7429,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,151): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,126): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,106): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,82): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,71): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7435,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7441,74): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7441,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,142): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,129): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,118): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,107): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7447,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7453,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,143): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,132): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,113): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,100): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,87): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7459,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7465,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7471,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7471,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7471,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7477,100): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7477,67): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7483,74): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7483,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7489,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7495,111): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7495,100): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7495,81): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7495,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7501,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7507,98): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7507,79): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7507,58): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(7513,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12691,128): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12691,117): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12691,106): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12691,95): warning C4100: “arg_1”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12691,76): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12697,121): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12697,110): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12697,99): warning C4100: “arg_1”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12697,80): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,140): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,129): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,118): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,107): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,96): warning C4100: “arg_1”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(12703,85): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17107,94): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17107,68): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17107,49): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17113,76): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17113,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17113,48): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17119,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17125,49): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17131,48): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17137,49): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17143,62): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(17149,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18721,80): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18721,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18721,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18727,80): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18727,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18727,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,133): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,120): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,107): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,94): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,81): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,68): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18733,55): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18739,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18745,106): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18745,93): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18745,80): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18745,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18745,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18751,108): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18751,95): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18751,82): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18751,69): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18751,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18757,108): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18757,95): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18757,82): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18757,69): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18757,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18763,106): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18763,93): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18763,80): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18763,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18763,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18957,61): warning C4458: “language”的声明隐藏了类成员
      E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\include\mupdf\classes.h(8858,7):
      参见“mupdf::FzPdfocrOptions::language”的声明
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(18963,60): warning C4458: “datadir”的声明隐藏了类成员
      E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\include\mupdf\classes.h(8859,7):
      参见“mupdf::FzPdfocrOptions::datadir”的声明
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,177): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,148): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,129): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,118): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,94): warning C4100: “arg_1”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38352,71): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38527,80): warning C4100: “arg_1”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(38527,60): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50044,63): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50050,62): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50056,63): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50062,80): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50062,62): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50068,68): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50074,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50074,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50080,63): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50080,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50086,63): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50086,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50092,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50092,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50098,83): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50098,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50098,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50104,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50104,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50110,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50110,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50116,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50116,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50116,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50122,75): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50122,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50128,69): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50128,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50134,69): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50134,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,144): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,126): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,115): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,101): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50140,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50146,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50152,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50158,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,131): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,118): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,105): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50164,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50170,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50170,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50170,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50176,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50176,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50176,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,130): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,117): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,104): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,91): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50182,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50188,104): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50188,91): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50188,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50188,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50188,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50194,104): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50194,91): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50194,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50194,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50194,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50200,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50206,105): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50206,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50206,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50206,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50206,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50212,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50218,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50224,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50230,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50236,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50242,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50248,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50254,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50260,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50266,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50272,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50278,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50284,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50290,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50296,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50296,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50302,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50302,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50308,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50308,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50314,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50314,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50320,109): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50320,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50320,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50320,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50326,64): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50326,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50332,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50332,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50338,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50338,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50338,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50344,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50344,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50344,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,131): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,118): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,105): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50350,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50356,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50362,71): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50362,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50368,92): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50368,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50368,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50374,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50374,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50374,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50380,122): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50380,96): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50380,83): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50380,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50380,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50386,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50386,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50386,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,131): warning C4100: “arg_7”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,118): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,105): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50392,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50398,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50398,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50398,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50404,96): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50404,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50404,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50410,127): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50410,113): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50410,102): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50410,80): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50410,61): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50416,127): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50416,113): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50416,102): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50416,80): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50416,61): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50422,97): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50422,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50422,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50428,97): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50428,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50428,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50434,84): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50434,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50434,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50440,84): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50440,70): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50440,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50446,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50446,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50452,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50452,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50458,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50458,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50458,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50458,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50464,92): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50464,79): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50464,66): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50464,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50470,104): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50470,91): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50470,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50470,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50470,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50476,104): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50476,91): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50476,78): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50476,65): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50476,52): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50482,91): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50482,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50482,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50488,91): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50488,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50488,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50494,97): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50494,78): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50494,59): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50500,95): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50500,77): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50500,58): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50506,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50506,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50512,108): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50512,90): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50512,72): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50512,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50518,73): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50518,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50524,109): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50524,91): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50524,73): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50524,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50530,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50536,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50542,53): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50548,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50548,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50554,67): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50554,56): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50560,68): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50560,57): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50566,86): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50566,68): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50572,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(50578,54): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51121,154): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51121,137): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51121,118): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51121,99): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51121,80): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,148): warning C4100: “arg_6”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,131): warning C4100: “arg_5”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,112): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,93): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,82): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51127,70): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51133,143): warning C4100: “arg_4”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51133,124): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51133,100): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51133,77): warning C4100: “arg_0”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51139,104): warning C4100: “arg_3”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51139,82): warning C4100: “arg_2”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\classes.cpp(51139,65): warning C4100: “arg_0”: 未引用的形参
  classes2.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes2.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes2.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/classes2.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes2.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/classes2.cpp”)
  
  exceptions.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/exceptions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/exceptions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/exceptions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/exceptions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/exceptions.cpp”)
  
  extra.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/extra.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/extra.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/extra.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/extra.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/extra.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(20,6): warning C4189: “e2”: 局部变量已初始化但不引用
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(37,6): warning C4189: “e2”: 局部变量已初始化但不引用
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(51,57): warning C4100: “ctx”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(147,6): warning C4189: “n2”: 局部变量已初始化但不引用
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(173,6): warning C4189: “n”: 局部变量已初始化但不引用
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(203,49): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(208,46): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(213,13): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(228,52): warning C4100: “ctx”: 未引用的形参
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\c++\implementation\extra.cpp(244,66): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
  functions.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/functions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/functions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/functions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/functions.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/functions.cpp”)
  
  internal.cpp
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(935,32): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/internal.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\context.h(950,33): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/internal.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(409,2): warning C4611: “_setjmp”和 C++ 对象析构之间的交互是不可移植的
  (编译源文件“../c++/implementation/internal.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(501,47): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/internal.cpp”)
  
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\include\mupdf\fitz\stream.h(621,45): warning C4100: “ctx”: 未引用的形参
  (编译源文件“../c++/implementation/internal.cpp”)
  
    正在创建库 x64\Release\mupdfcpp64.lib 和对象 x64\Release\mupdfcpp64.exp
  正在生成代码
  8 of 48821 functions (<0.1%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    1 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  mupdfcpp.vcxproj -> E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\win32\x64\Release\mupdfcpp64.dll
