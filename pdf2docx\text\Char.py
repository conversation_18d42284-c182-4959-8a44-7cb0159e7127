# -*- coding: utf-8 -*-

'''Char object based on PDF raw dict extracted with ``PyMuPDF``.

Data structure refer to this `link <https://pymupdf.readthedocs.io/en/latest/textpage.html>`_::

    {
        'bbox'  : (x0, y0, x1, y1), 
        'c'     : str, 
        'origin': (x,y)
    }
'''


from ..common.constants import INVALID_CHARS
from ..common.Element import Element
from ..shape.Shape import Shape
import fitz

class Char(Element):
    '''Object representing a character.'''
    def __init__(self, raw:dict=None):
        if raw is None: raw = {}

        # Note to filter control character avoiding error when making docx, #126
        c = raw.get('c', '')
        if c in INVALID_CHARS: c = ''
        self.c = c
        self.origin = raw.get('origin', None)

        # 保存字形边界框信息
        self.gbox = raw.get('gbox', None)

        super().__init__(raw) # NOTE: ignore parent element for Char instance

    def contained_in_rect(self, rect:Shape, horizontal:bool=True):
        """Detect whether it locates in a rect.

        Args:
            rect (Shape): Target rect to check.
            horizontal (bool, optional): Text direction is horizontal if True. Defaults to True.

        Returns:
            bool: Whether a Char locates in target rect.

        .. note::
            It's considered as contained in the target rect if the intersection is larger than 
            half of the char bbox.
        """
        # char in rect?
        if self.bbox in rect.bbox: return True

        # intersection?
        s = self.bbox & rect.bbox
        if s.is_empty: return False
        if horizontal: return s.width > 0.5*self.bbox.width
        return s.height > 0.5*self.bbox.height


    def store(self):
        res = super().store()
        res.update({
            'c': self.c,
            'origin': self.origin,
            'gbox': self.gbox
        })

        return res

    def _is_punct(self):
        '''
        Returns:
            bool: True if the character is a punctuation mark, False otherwise.
        '''
        # 中文标点符号
        _cn_puncts = '，。！？；：、""''（）【】《》〈〉…'
        # 英文标点符号
        _en_puncts = '.,:;!?\'\"()[]\{}@#$%^&*_+-=<>/\\|~`'
        return self.c in _cn_puncts or self.c in _en_puncts

    def _is_cjk(self):
        '''
        Returns:
            bool: True if the character is a CJK character, False otherwise.
        '''
        if not self.c: return False
        try:
            char = ord(self.c)
        except:
            return False
        
        # CJK Unified Ideographs
        if 0x4E00 <= char <= 0x9FFF: return True
        # CJK Unified Ideographs Extension A
        if 0x3400 <= char <= 0x4DBF: return True
        # CJK Unified Ideographs Extension B
        if 0x20000 <= char <= 0x2A6DF: return True
        # CJK Unified Ideographs Extension C
        if 0x2A700 <= char <= 0x2B73F: return True
        # CJK Unified Ideographs Extension D
        if 0x2B740 <= char <= 0x2B81F: return True
        # CJK Unified Ideographs Extension E
        if 0x2B820 <= char <= 0x2CEAF: return True
        # CJK Unified Ideographs Extension F
        if 0x2CEB0 <= char <= 0x2EBEF: return True
        
        # CJK相关标点符号范围
        # CJK Symbols and Punctuation
        if 0x3000 <= char <= 0x303F: return True
        # CJK Compatibility Forms
        if 0xFE30 <= char <= 0xFE4F: return True
        # Halfwidth and Fullwidth Forms (CJK标点符号部分)
        if 0xFF00 <= char <= 0xFF60: return True
        # Small Form Variants
        if 0xFE50 <= char <= 0xFE6F: return True
        # Vertical Forms
        if 0xFE10 <= char <= 0xFE1F: return True
        # CJK Compatibility Ideographs
        if 0xF900 <= char <= 0xFAFF: return True
        # CJK Compatibility Ideographs Supplement
        if 0x2F800 <= char <= 0x2FA1F: return True
        # CJK Symbols and Punctuation
        if 0x3000 <= char <= 0x303F: return True
        # Hiragana
        if 0x3040 <= char <= 0x309F: return True
        # Katakana
        if 0x30A0 <= char <= 0x30FF: return True
        # Katakana Phonetic Extensions
        if 0x31F0 <= char <= 0x31FF: return True
        # Hangul Syllables
        if 0xAC00 <= char <= 0xD7AF: return True
        # Hangul Jamo
        if 0x1100 <= char <= 0x11FF: return True
        # Hangul Compatibility Jamo
        if 0x3130 <= char <= 0x318F: return True
        # Hangul Jamo Extended-A
        if 0xA960 <= char <= 0xA97F: return True
        # Hangul Jamo Extended-B
        if 0xD7B0 <= char <= 0xD7FF: return True
        # Kangxi Radicals
        if 0x2F00 <= char <= 0x2FDF: return True
        # CJK Radicals Supplement
        if 0x2E80 <= char <= 0x2EFF: return True
        # Ideographic Description Characters
        if 0x2FF0 <= char <= 0x2FFF: return True
        # Bopomofo
        if 0x3100 <= char <= 0x312F: return True
        # Bopomofo Extended
        if 0x31A0 <= char <= 0x31BF: return True
        # Kanbun
        if 0x3190 <= char <= 0x319F: return True
        # CJK Strokes
        if 0x31C0 <= char <= 0x31EF: return True
        # Enclosed CJK Letters and Months
        if 0x3200 <= char <= 0x32FF: return True
        # CJK Compatibility
        if 0x3300 <= char <= 0x33FF: return True
        # CJK Compatibility Forms
        if 0xFE30 <= char <= 0xFE4F: return True
        
        return False