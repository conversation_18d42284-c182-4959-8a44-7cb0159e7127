﻿  mucbz.c
  muimg.c
  archive.c
  bbox-device.c
  bidi-std.c
  bidi.c
  bitmap.c
  brotli.c
  buffer.c
  color-fast.c
  color-icc-create.c
  color-lcms.c
  colorspace.c
  compress.c
  compressed-buffer.c
  context.c
  crypt-aes.c
  crypt-arc4.c
  crypt-md5.c
  crypt-sha2.c
  正在编译...
  deskew.c
  device.c
  directory.c
  document-all.c
  document.c
  draw-affine.c
  draw-blend.c
  draw-device.c
  draw-edge.c
  draw-edgebuffer.c
  draw-glyph.c
  draw-mesh.c
  draw-paint.c
  draw-path.c
  draw-rasterize.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\draw-path.c(853,8): warning C4127: 条件表达式是常量
  draw-scale-simple.c
  draw-unpack.c
  encode-basic.c
  encode-fax.c
  encode-jpx.c
  正在编译...
  encodings.c
  error.c
  filter-basic.c
  filter-brotli.c
  filter-dct.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\filter-dct.c(49,12): warning C4324: “<unnamed-tag>”: 由于对齐说明符，结构被填充
  filter-fax.c
  filter-flate.c
  filter-jbig2.c
  filter-leech.c
  filter-lzw.c
  filter-predict.c
  filter-sgi.c
  filter-thunder.c
  font.c
  ftoa.c
  geometry.c
  glyph.c
  glyphbox.c
  gz-doc.c
  halftone.c
  正在编译...
  harfbuzz.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\thirdparty\harfbuzz\src\hb-style.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../source/fitz/harfbuzz.c”)
  
  hash.c
  heap.c
  image.c
  json.c
  link.c
  list-device.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\list-device.c(192,27): warning C4127: 条件表达式是常量
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\list-device.c(195,27): warning C4127: 条件表达式是常量
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\list-device.c(207,27): warning C4127: 条件表达式是常量
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\list-device.c(211,27): warning C4127: 条件表达式是常量
  load-bmp.c
  load-gif.c
  load-jbig2.c
  load-jpeg.c
  load-jpx.c
  load-jxr.c
  load-png.c
  load-pnm.c
  load-psd.c
  load-tiff.c
  log.c
  memory.c
  noto.c
  正在编译...
  ocr-device.c
  outline.c
  output-cbz.c
  output-csv.c
  output-docx.c
  output-jpeg.c
  output-pcl.c
  output-pclm.c
  output-pdfocr.c
  output-png.c
  output-pnm.c
  output-ps.c
  output-psd.c
  output-pwg.c
  output-svg.c
  output.c
  path.c
  pixmap.c
  pool.c
  printf.c
  正在编译...
  random.c
  separation.c
  shade.c
  skew.c
  stext-boxer.c
  stext-device.c
  stext-output.c
  stext-para.c
  stext-search.c
  stext-table.c
  store.c
  stream-open.c
  stream-read.c
  string.c
  strtof.c
  subset-cff.c
  subset-ttf.c
  svg-device.c
  test-device.c
  text-decoder.c
  正在编译...
  text.c
  time.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\fitz\time.c(39,45): warning C4115: “timezone”: 括号中的已命名类型定义
  trace-device.c
  track-usage.c
  transition.c
  tree.c
  ucdn.c
  uncfb.c
  unlibarchive.c
  untar.c
  unzip.c
  util.c
  warp.c
  writer.c
  xml-write.c
  xml.c
  xmltext-device.c
  zip.c
  css-apply.c
  css-parse.c
\source\html\css-properties.gperf(49,88): warning C4267: “return”: 从“size_t”转换到“unsigned int”，可能丢失数据
  (编译源文件“../../source/html/css-parse.c”)
  
  正在编译...
  epub-doc.c
  html-doc.c
  html-font.c
  html-layout.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\thirdparty\harfbuzz\src\hb-style.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../source/html/html-layout.c”)
  
  html-outline.c
  html-parse.c
  mobi.c
  office.c
  txt.c
  xml-dom.c
  pdf-af.c
  pdf-annot.c
  pdf-appearance.c
  pdf-clean-file.c
  pdf-clean.c
  pdf-cmap-load.c
  pdf-cmap-parse.c
  pdf-cmap.c
  pdf-colorspace.c
  pdf-crypt.c
  正在编译...
  pdf-device.c
  pdf-event.c
  pdf-font-add.c
  pdf-font.c
  pdf-form.c
  pdf-function.c
  pdf-graft.c
  pdf-image-rewriter.c
  pdf-image.c
  pdf-interpret.c
  pdf-js.c
  pdf-label.c
  pdf-layer.c
  pdf-layout.c
  pdf-lex.c
  pdf-link.c
  pdf-metrics.c
  pdf-nametree.c
  pdf-object.c
  pdf-op-buffer.c
  正在编译...
  pdf-op-color.c
E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\source\pdf\pdf-op-color.c(1520,8): warning C4127: 条件表达式是常量
  pdf-op-filter.c
  pdf-op-run.c
  pdf-outline.c
  pdf-page.c
  pdf-parse.c
  pdf-pattern.c
  pdf-recolor.c
  pdf-repair.c
  pdf-resources.c
  pdf-run.c
  pdf-shade-recolor.c
  pdf-shade.c
  pdf-signature.c
  pdf-store.c
  pdf-stream.c
  pdf-subset.c
  pdf-type3.c
  pdf-unicode.c
  pdf-util.c
  正在编译...
  pdf-write.c
  pdf-xobject.c
  pdf-xref.c
  pdf-zugferd.c
  reflow-doc.c
  svg-color.c
  svg-doc.c
  svg-parse.c
  svg-run.c
  xps-common.c
  xps-doc.c
  xps-glyphs.c
  xps-gradient.c
  xps-image.c
  xps-link.c
  xps-outline.c
  xps-path.c
  xps-resource.c
  xps-tile.c
  xps-util.c
  正在编译...
  xps-zip.c
  libmupdf.vcxproj -> E:\pdf2docx4\PyMuPDF\mupdf-1.26.2-source\platform\win32\x64\Release\libmupdf.lib
