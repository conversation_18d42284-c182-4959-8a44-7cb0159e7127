#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复PyMuPDF元数据的脚本
"""

import os
import sys
import site
import glob
import shutil
from pathlib import Path

def find_pymupdf_dist_info():
    """查找PyMuPDF的dist-info目录"""
    
    site_packages = site.getsitepackages()[0]
    print(f"site-packages路径: {site_packages}")
    
    # 查找所有可能的PyMuPDF dist-info目录
    patterns = [
        "pymupdf*.dist-info",
        "PyMuPDF*.dist-info",
        "PYMUPDF*.dist-info"
    ]
    
    dist_info_dirs = []
    for pattern in patterns:
        search_path = os.path.join(site_packages, pattern)
        found = glob.glob(search_path)
        dist_info_dirs.extend(found)
    
    print(f"找到的dist-info目录: {dist_info_dirs}")
    return dist_info_dirs

def backup_metadata(dist_info_path):
    """备份原始元数据"""
    
    metadata_path = os.path.join(dist_info_path, "METADATA")
    if os.path.exists(metadata_path):
        backup_path = metadata_path + ".backup"
        shutil.copy2(metadata_path, backup_path)
        print(f"备份元数据: {backup_path}")
        return True
    return False

def fix_metadata_file(dist_info_path):
    """修复METADATA文件"""
    
    metadata_path = os.path.join(dist_info_path, "METADATA")
    
    if not os.path.exists(metadata_path):
        print(f"❌ METADATA文件不存在: {metadata_path}")
        return False
    
    # 读取现有内容
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始METADATA内容前10行:")
        lines = content.split('\n')
        for i, line in enumerate(lines[:10]):
            print(f"  {i+1}: {line}")
        
        # 检查是否已经有正确的Metadata-Version
        has_metadata_version = False
        has_correct_version = False
        
        new_lines = []
        for line in lines:
            if line.startswith('Metadata-Version:'):
                has_metadata_version = True
                if line.strip() == 'Metadata-Version:' or 'Metadata-Version: ' in line and not line.split(':', 1)[1].strip():
                    # 修复空的Metadata-Version
                    new_lines.append('Metadata-Version: 2.1')
                    has_correct_version = True
                    print("✅ 修复了空的Metadata-Version")
                else:
                    new_lines.append(line)
                    has_correct_version = True
            else:
                new_lines.append(line)
        
        # 如果没有Metadata-Version，添加一个
        if not has_metadata_version:
            new_lines.insert(0, 'Metadata-Version: 2.1')
            print("✅ 添加了缺失的Metadata-Version")
            has_correct_version = True
        
        if has_correct_version:
            # 写回文件
            new_content = '\n'.join(new_lines)
            with open(metadata_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 修复了METADATA文件")
            
            # 显示修复后的前几行
            print(f"修复后的METADATA内容前5行:")
            for i, line in enumerate(new_content.split('\n')[:5]):
                print(f"  {i+1}: {line}")
            
            return True
        else:
            print("ℹ️ METADATA文件已经正确，无需修复")
            return True
            
    except Exception as e:
        print(f"❌ 修复METADATA文件失败: {e}")
        return False

def create_missing_metadata(dist_info_path):
    """如果METADATA文件不存在，创建一个"""
    
    metadata_path = os.path.join(dist_info_path, "METADATA")
    
    if os.path.exists(metadata_path):
        return True
    
    print(f"创建缺失的METADATA文件: {metadata_path}")
    
    # 获取PyMuPDF版本信息
    try:
        import pymupdf
        version = pymupdf.__version__
        pymupdf_version = getattr(pymupdf, 'pymupdf_version', version)
        pymupdf_date = getattr(pymupdf, 'pymupdf_date', '2025-01-11 00:00:01')
    except:
        version = "1.26.2"
        pymupdf_version = "1.26.2"
        pymupdf_date = "2025-01-11 00:00:01"
    
    metadata_content = f"""Metadata-Version: 2.1
Name: PyMuPDF
Version: {version}
Summary: A high performance Python library for data extraction, analysis, conversion & manipulation of PDF (and other) documents.
Description-Content-Type: text/markdown
Author: Artifex
Author-email: <EMAIL>
License: Dual Licensed - GNU AFFERO GPL 3.0 or Artifex Commercial License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: C
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Utilities
Classifier: Topic :: Multimedia :: Graphics
Requires-Python: >=3.9

# PyMuPDF

PyMuPDF is a high performance Python library for data extraction, analysis, conversion & manipulation of PDF (and other) documents.

Version: {pymupdf_version}
Date: {pymupdf_date}
"""
    
    try:
        with open(metadata_path, 'w', encoding='utf-8') as f:
            f.write(metadata_content)
        
        print(f"✅ 创建了METADATA文件")
        return True
        
    except Exception as e:
        print(f"❌ 创建METADATA文件失败: {e}")
        return False

def test_pip_show():
    """测试pip show是否修复"""
    
    print(f"\n=== 测试pip show ===")
    
    import subprocess
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'show', 'pymupdf'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ pip show pymupdf 修复成功!")
            print("输出:")
            print(result.stdout)
            return True
        else:
            print(f"❌ pip show 仍然失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试pip show失败: {e}")
        return False

def main():
    """主函数"""
    
    print("PyMuPDF元数据修复工具")
    print("=" * 50)
    
    # 1. 查找dist-info目录
    dist_info_dirs = find_pymupdf_dist_info()
    
    if not dist_info_dirs:
        print("❌ 未找到PyMuPDF的dist-info目录")
        print("可能的原因:")
        print("1. PyMuPDF未正确安装")
        print("2. 安装方式特殊，没有生成标准的dist-info")
        return False
    
    success = True
    
    # 2. 处理每个找到的dist-info目录
    for dist_info_path in dist_info_dirs:
        print(f"\n处理: {dist_info_path}")
        
        # 备份
        backup_metadata(dist_info_path)
        
        # 修复或创建METADATA
        if os.path.exists(os.path.join(dist_info_path, "METADATA")):
            if not fix_metadata_file(dist_info_path):
                success = False
        else:
            if not create_missing_metadata(dist_info_path):
                success = False
    
    # 3. 测试修复结果
    if success:
        test_pip_show()
    
    return success

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 PyMuPDF元数据修复完成!")
        print(f"现在可以正常使用 'pip show pymupdf' 了")
    else:
        print(f"\n❌ 修复过程中出现问题")
        print(f"建议使用替代方案检查版本:")
        print(f"python -c \"import pymupdf; print(pymupdf.__version__)\"")
    
    sys.exit(0 if success else 1)
