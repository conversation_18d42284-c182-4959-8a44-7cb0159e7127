'''Main frame with grouped controls.'''

import os
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import fitz
from PIL import Image, ImageDraw, ImageTk, ImageFont
import json
import tempfile
from ..image.ImageBlock import ImageBlock
from ..table.TableBlock import TableBlock
from ..text.TextBlock import TextBlock
from ..text.TocBlock import TocBlock
from ..text.TextFloatBlock import TextFloatBlock
from ..common.share import TextDirection
from ..converter import Converter
from ..common.Element import Element

class MainFrame(tk.Frame):
    def __init__(self, parent:tk=None):
        '''Main frame with grouped controls. This should be attached to a parent ``Tk`` instance.'''
        super().__init__(parent)
        self.configure(bg='#f0f0f0')  # 设置整体背景色为浅灰色
        self.parent = parent
        # Initialize variables
        self.auto_open_var = tk.BooleanVar(value=False)
        self.show_char_bbox_var = tk.BooleanVar(value=False)  # 添加字符边框显示控制变量
        self.show_line_bg_var = tk.BooleanVar(value=False)    # 添加Line背景显示控制变量
        self.show_span_bg_var = tk.BooleanVar(value=False)    # 添加Span背景显示控制变量
        self.show_bbox_info_var = tk.BooleanVar(value=True)   # 添加框线坐标显示控制变量(默认显示)
        self.show_spacing_info_var = tk.BooleanVar(value=True) # 添加段前/段后距离提示显示控制变量(默认显示)
        self.show_projection_var = tk.BooleanVar(value=False)  # 添加投影数据显示控制变量
        self.show_baseline_var = tk.BooleanVar(value=False)   # 添加基线显示控制变量
        self.grid(ipadx=100, ipady=100)

        # config file path (use system temp dir)
        self.config_path = os.path.join(tempfile.gettempdir(), 'pdf2docx_config.json')

        # variables
        self.pdf_paths = set() # unique pdf files
        self.docx_folder = None
        self.preview_windows = {}  # 存储每个PDF文件对应的预览窗口
        self.preview_states = {}  # 存储每个预览窗口的状态

        # Load last config if exists
        self.last_pdf_file = ''
        self.last_output_dir = ''
        self.last_auto_open = False
        self._load_last_config()

        self.setup_ui()

        # 自动填充 entry
        if self.last_pdf_file:
            self.file_path_pdf_entry.delete(0, 'end')
            self.file_path_pdf_entry.insert(0, self.last_pdf_file)
            self.pdf_paths = set(self.last_pdf_file.split(';'))
        if self.last_output_dir:
            self.file_path_docx_entry.delete(0, 'end')
            self.file_path_docx_entry.insert(0, self.last_output_dir)
            self.docx_folder = self.last_output_dir
        self.auto_open_var.set(self.last_auto_open)

    def _load_last_config(self):
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.last_pdf_file = config.get('last_pdf_file', '')
                    self.last_output_dir = config.get('last_output_dir', '')
                    self.last_auto_open = config.get('auto_open', False)
        except Exception as e:
            print(f"Failed to load config: {e}")
            self.last_pdf_file = ''
            self.last_output_dir = ''
            self.last_auto_open = False

    def _save_last_config(self):
        try:
            config = {
                'last_pdf_file': self.last_pdf_file,
                'last_output_dir': self.last_output_dir,
                'auto_open': self.auto_open_var.get()
            }
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f)
        except Exception as e:
            print(f"Failed to save config: {e}")

    def setup_ui(self):
        '''Layout of the user interface.'''
        # 创建主标题框架
        title_frame = ttk.Frame(self)
        title_frame.grid(row=0, column=0, columnspan=2, pady=(5, 5))
        
        # 创建图标标签
        icon_label = tk.Label(title_frame, text='📄', font='Arial 24', bg='#f0f0f0', fg='#2196F3')
        icon_label.pack(side='left', padx=5)
        
        # PDF to Docx Label
        self.program_use_label = tk.Label(title_frame, text='PDF to DOCX', 
            font=('Helvetica', 24, 'bold'),
            bg='#f0f0f0', 
            fg='#2196F3')
        self.program_use_label.pack(side='left')

        # 创建文件选择区域框架
        file_frame = ttk.LabelFrame(self, text='File Selection', padding=(0, 8, 15, 12))
        file_frame.grid(row=1, column=0, columnspan=2, padx=(0, 50), pady=(0, 10), sticky='nsew')
        
        # PDF文件选择区域
        pdf_label = tk.Label(file_frame, text='PDF File:', font=('Helvetica', 10), bg='#f0f0f0')
        pdf_label.grid(row=0, column=0, sticky='w', pady=(0, 10), padx=0)
        
        self.file_path_pdf_entry = tk.Entry(file_frame, 
            font=('Helvetica', 10),
            width=45,
            relief='solid',
            bg='white')
        self.file_path_pdf_entry.grid(row=0, column=1, sticky='ew', padx=(5, 10), pady=(0, 10))
        
        self.select_pdf_file = tk.Button(file_frame, text='Browse...', 
            font=('Helvetica', 10),
            bg='#e0e0e0',
            fg='black',
            relief='solid',
            command=self._callback_pdf_file_location)
        self.select_pdf_file.grid(row=0, column=2, padx=(0, 5), pady=(0, 10))
        
        # Docx文件选择区域
        docx_label = tk.Label(file_frame, text='Output:', font=('Helvetica', 10), bg='#f0f0f0')
        docx_label.grid(row=1, column=0, sticky='w', padx=0)
        
        self.file_path_docx_entry = tk.Entry(file_frame,
            font=('Helvetica', 10),
            width=45,
            relief='solid',
            bg='white')
        self.file_path_docx_entry.grid(row=1, column=1, sticky='ew', padx=(5, 10))
        
        self.select_new_file_folder = tk.Button(file_frame, text='Browse...', 
            font=('Helvetica', 10),
            bg='#e0e0e0',
            fg='black',
            relief='solid',
            command=self._callback_docx_folder_location)
        self.select_new_file_folder.grid(row=1, column=2, padx=(0, 5))
        
        # 添加自动打开文件的复选框
        self.auto_open_checkbox = tk.Checkbutton(file_frame, 
            text='Auto open file after conversion',
            variable=self.auto_open_var,
            font=('Helvetica', 10),
            bg='#f0f0f0',
            state='normal',
            command=self._callback_auto_open_checkbox)
        self.auto_open_checkbox.grid(row=2, column=1, sticky='w', padx=(5, 10), pady=(10, 0))

        # 配置文件框架的列权重
        file_frame.grid_columnconfigure(1, weight=1)

        # 创建操作按钮框架并居中对齐
        button_frame = ttk.Frame(self)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        # 设置按钮样式
        button_style = {
            'font': ('Helvetica', 12, 'bold'),
            'width': 12,  # 稍微减小按钮宽度
            'height': 2,
            'relief': 'solid',
            'cursor': 'hand2'
        }

        # 创建内部框架用于按钮居中
        inner_button_frame = ttk.Frame(button_frame)
        inner_button_frame.pack(expand=True)

        # Convert Button
        self.converter_button = tk.Button(inner_button_frame, text='Convert',
            bg='#2196F3',
            fg='white',
            activebackground='#1976D2',
            activeforeground='white',
            **button_style)
        self.converter_button.pack(side='left', padx=10)
        self.converter_button.configure(command=self._callback_convert)

        # Preview Button
        self.preview_button = tk.Button(inner_button_frame, text='Preview',
            bg='#4CAF50',
            fg='white',
            activebackground='#388E3C',
            activeforeground='white',
            **button_style)
        self.preview_button.pack(side='left', padx=10)
        self.preview_button.configure(command=self._callback_preview)

        # 创建提示信息框架
        info_frame = ttk.Frame(self)
        info_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        # 警告信息
        self.check_label = tk.Label(info_frame, 
            text='⚠️ This converter can only convert text-based PDF files',
            font=('Helvetica', 10),
            bg='#FFF3E0',  # 浅橙色背景
            fg='#FF9800',  # 橙色文字
            pady=10,
            padx=20)
        self.check_label.pack(fill='x')

        # 帮助按钮
        self.file_check_button = tk.Button(info_frame, 
            text='How to check my file?',
            font=('Helvetica', 9, 'underline'),
            fg='#2196F3',
            bg='#f0f0f0',
            bd=0,
            cursor='hand2',
            command=lambda: webbrowser.open_new(r"https://support.policystat.com/hc/en-us/articles"
                        r"/207993346-How-can-I-tell-if-my-PDFs-are-text-based-or-not-"))
        self.file_check_button.pack(pady=5)

    def get_glyph_bbox(self, char, page_idx, pdf_path):
        """获取字符的字形边界框
        Args:
            char: 字符对象
            page_idx: 页面索引
            pdf_path: PDF文件路径
        Returns:
            字形边界框坐标 [x0, y0, x1, y1] 或 None
        """
        # 缓存texttrace数据以提高性能
        if not hasattr(self, '_texttrace_cache'):
            self._texttrace_cache = {}

        cache_key = f"{pdf_path}_{page_idx}"
        if cache_key not in self._texttrace_cache:
            try:
                doc = fitz.open(pdf_path)
                page = doc[page_idx]
                self._texttrace_cache[cache_key] = page.get_texttrace()
                doc.close()
            except Exception as e:
                print(f"Warning: Could not get texttrace: {str(e)}")
                return None

        texttrace = self._texttrace_cache[cache_key]

        try:
            # 查找对应的字符信息
            char_bbox = char.bbox
            char_c = char.c

            if not char_c:  # 跳过空字符
                return None

            char_unicode = ord(char_c)

            # 在texttrace中查找匹配的字符
            for span in texttrace:
                if isinstance(span, dict) and 'chars' in span:
                    for trace_char in span['chars']:
                        if len(trace_char) >= 4:  # [unicode, glyph_id, (x, y), bbox]
                            trace_unicode = trace_char[0]
                            trace_glyph_id = trace_char[1]
                            trace_bbox = trace_char[3] if len(trace_char) > 3 else None

                            # 检查unicode匹配
                            if trace_unicode == char_unicode and trace_bbox and len(trace_bbox) == 4:
                                # 检查位置是否接近（放宽匹配条件）
                                if (abs(trace_bbox[0] - char_bbox[0]) < 2.0 and
                                    abs(trace_bbox[1] - char_bbox[1]) < 2.0):

                                    # 使用texttrace中的边界框作为字形边界框
                                    x0, y0, x1, y1 = trace_bbox

                                    # 如果字形ID大于0，说明有实际的字形
                                    if trace_glyph_id > 0:
                                        # 对于大多数字符，字形边界框会比字符边界框稍小
                                        char_width = x1 - x0
                                        char_height = y1 - y0

                                        # 字形边界框通常在字符边界框内部，留出一些边距
                                        margin_x = char_width * 0.08  # 8%的水平边距
                                        margin_y = char_height * 0.03  # 3%的垂直边距

                                        glyph_x0 = x0 + margin_x
                                        glyph_y0 = y0 + margin_y
                                        glyph_x1 = x1 - margin_x
                                        glyph_y1 = y1 - margin_y

                                        return [glyph_x0, glyph_y0, glyph_x1, glyph_y1]

        except Exception as e:
            print(f"Warning: Could not get glyph bbox: {str(e)}")

        return None

    def draw_bbox_info(self, draw, bbox, color, font, use_zoom=False, zoom_scale=1.0):
        """在边界框右上角绘制位置和大小信息
        Args:
            draw: PIL.ImageDraw对象
            bbox: 边界框坐标 [x0, y0, x1, y1]
            color: 文字颜色
            font: PIL.ImageFont对象
            use_zoom: 是否使用缩放比例
            zoom_scale: 缩放比例，默认为1.0
        """
        x0, y0, x1, y1 = bbox
        # 如果不使用缩放，需要将坐标转换回原始大小
        if not use_zoom:
            x0, y0, x1, y1 = [coord / zoom_scale for coord in [x0, y0, x1, y1]]
        width = x1 - x0
        height = y1 - y0
        text = f"({round(x0, 1)},{round(y0, 1)})-({round(width, 1)},{round(height, 1)})"

        # 计算文本大小
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        # 计算文本位置（右上角）
        if use_zoom:
            text_x = x1 - text_width - 5
            text_y = y0 + 5
        else:
            # 如果不使用缩放，需要将位置转换为缩放后的坐标
            text_x = (x1 * zoom_scale) - text_width - 5
            text_y = (y0 * zoom_scale) + 5
        
        # 绘制白色背景确保文字清晰可见
        padding = 2
        draw.rectangle([text_x - padding, text_y - padding,
                       text_x + text_width + padding, text_y + text_height + padding],
                      fill='white')
        
        # 绘制文本
        draw.text((text_x, text_y), text, font=font, fill=color)

    def draw_dashed_rectangle(self, draw, bbox, color, dash_length, gap_length, width):
        """绘制虚线矩形
        Args:
            draw: PIL.ImageDraw对象
            bbox: 边界框坐标 [x0, y0, x1, y1]，已经是缩放后的坐标
            color: 线条颜色
            dash_length: 虚线段长度，已经是缩放后的长度
            gap_length: 虚线间隔长度，已经是缩放后的长度
            width: 线条宽度，已经是缩放后的宽度
        """
        x0, y0, x1, y1 = bbox
        
        # 画上边
        x, y = x0, y0
        while x < x1:
            end_x = min(x + dash_length, x1)
            draw.line([(x, y), (end_x, y)], fill=color, width=width)
            x += dash_length + gap_length
        
        # 画右边
        x, y = x1, y0
        while y < y1:
            end_y = min(y + dash_length, y1)
            draw.line([(x, y), (x, end_y)], fill=color, width=width)
            y += dash_length + gap_length
        
        # 画下边
        x, y = x1, y1
        while x > x0:
            start_x = max(x - dash_length, x0)
            draw.line([(start_x, y), (x, y)], fill=color, width=width)
            x -= dash_length + gap_length
        
        # 画左边
        x, y = x0, y1
        while y > y0:
            start_y = max(y - dash_length, y0)
            draw.line([(x, start_y), (x, y)], fill=color, width=width)
            y -= dash_length + gap_length

    def draw_spacing_info(self, draw, block, bbox, color, font):
        """绘制 TextBlock 的 spacing 信息
        
        Args:
            draw: PIL.ImageDraw对象
            block: TextBlock对象
            bbox: 边界框坐标 [x0, y0, x1, y1]，已经是缩放后的坐标
            color: 文字颜色
            font: PIL.ImageFont对象
        """
        if not hasattr(block, 'before_space') and not hasattr(block, 'after_space'):
            return
            
        x0, y0, x1, y1 = bbox
        text_color = (128, 0, 128)  # 紫色
        
        # 绘制 before_space
        if hasattr(block, 'before_space'):
            before_text = f"↑{block.before_space:.1f}pt"
            # 在块的左上方显示
            draw.text((x0, y0-20), before_text, fill=text_color, font=font)
            
        # 绘制 after_space
        if hasattr(block, 'after_space'):
            after_text = f"↓{block.after_space:.1f}pt"
            # 在块的左下方显示
            draw.text((x0, y1+5), after_text, fill=text_color, font=font)

    def _callback_pdf_file_location(self):
        '''Opens file explorer and let you select choose the pdf file that you want to convert.'''
        # 获取当前 entry 的路径作为初始目录
        current_path = self.file_path_pdf_entry.get().strip()
        initialdir = ''
        if current_path:
            # 只取第一个路径
            first_path = current_path.split(';')[0].replace('"', '').replace("'", '')
            # 如果是文件，取其父目录
            if os.path.isfile(first_path):
                initialdir = os.path.dirname(first_path)
            elif os.path.isdir(first_path):
                initialdir = first_path
            elif os.path.isdir(os.path.dirname(first_path)):
                initialdir = os.path.dirname(first_path)
        file_paths = filedialog.askopenfilenames(filetypes=[('PDF file', '*.pdf')], initialdir=initialdir or None)
        if file_paths:
            self.pdf_paths = set()
            for path in file_paths:
                self.pdf_paths.add(path)
            # show just names
            names = ';'.join(self.pdf_paths)
            self.file_path_pdf_entry.delete(0, 'end')
            self.file_path_pdf_entry.insert(0, names)
            # 保存到 config.json
            self.last_pdf_file = names
            self._save_last_config()
        # 如果未选择，entry 保持原样



    def _callback_docx_folder_location(self):
        '''Opens file explorer and let you choose the folder, that all the converted file or files going to saved.'''
        # 获取当前 entry 的路径作为初始目录
        current_path = self.file_path_docx_entry.get().strip()
        initialdir = ''
        if current_path and os.path.isdir(current_path):
            initialdir = current_path
        elif current_path and os.path.isdir(os.path.dirname(current_path)):
            initialdir = os.path.dirname(current_path)
        folder = filedialog.askdirectory(initialdir=initialdir or None)
        if folder:
            self.docx_folder = folder
            self.file_path_docx_entry.delete(0, 'end')
            self.file_path_docx_entry.insert(0, self.docx_folder)
            # 保存到 config.json
            self.last_output_dir = self.docx_folder
            self._save_last_config()
        # 如果未选择，entry 保持原样

    def _callback_auto_open_checkbox(self):
        self._save_last_config()

    

    def _callback_convert(self):
        '''Starts the convert of the file or files.'''
        # input check
        if not self.pdf_paths and not self.docx_folder:
            messagebox.showwarning(
                title='Neither files or folder selected', 
                message='Select PDF file or files for convert '
                        'and Select a folder for the converted files!')
            return

        if not self.pdf_paths:
            messagebox.showwarning(
                title='Not files for convert selected', 
                message='Select PDF file or PDF files for convert!')
            return

        if not self.docx_folder:
            messagebox.showwarning(
                title='Not files folder selected', 
                message='Select a folder for the converted files!')
            return

        # collect docx files to convert to
        docx_paths = []
        for pdf_path in self.pdf_paths:
            base_name = os.path.basename(pdf_path)
            name, ext = os.path.splitext(base_name)
            docx_path = os.path.join(self.docx_folder, f'{name}.docx')
            docx_paths.append(docx_path)
        
        if any([os.path.exists(path) for path in docx_paths]) and \
            not messagebox.askokcancel(title='Existed target file', 
                message='Docx files with same target name are found under selected folder. '
                        'Do you want to continue and replace them?'):
            return
        
        # now, do the converting work
        num_succ, num_fail = 0, 0
        for pdf_path, docx_path in zip(self.pdf_paths, docx_paths):
            cv = Converter(pdf_path)
            try:
                cv.convert(docx_path)
            except Exception as e:
                print(e)
                num_fail += 1
            else:
                # open docx file if auto-open is checked
                if self.auto_open_var.get():
                    try:
                        os.startfile(docx_path)
                    except AttributeError:  # os.startfile is not available on non-Windows
                        subprocess.run(['start', docx_path], shell=True)
                    except Exception as e:
                        print(f"Error opening file: {e}")
                num_succ += 1
            finally:
                cv.close()

        messagebox.showinfo(title='Convert Done!', 
            message=f'Successful ({num_succ}), Failed ({num_fail}).')


    def _check_existing_preview(self, pdf_path):
        '''Check if a preview window already exists for the given PDF path'''
        if pdf_path in self.preview_windows:
            window = self.preview_windows[pdf_path]
            if window.winfo_exists():
                window.lift()  # 将窗口提升到最前面
                window.focus_force()  # 强制获取焦点
                return True
            else:
                # 如果窗口已经关闭，删除相关记录
                del self.preview_windows[pdf_path]
                if pdf_path in self.preview_states:
                    del self.preview_states[pdf_path]
        return False
    
    def _create_preview_window(self, pdf_path):
        '''Create a new preview window for the given PDF path'''
        # 创建预览窗口
        preview = tk.Toplevel()
        preview.title(f'Preview - {os.path.basename(pdf_path)}')
        preview.geometry('800x600')
        preview.configure(bg='#f0f0f0')  # 设置背景色为浅灰色
        
        # 初始化窗口状态
        state = {
            'zoom_scale': 1.0,
            'current_page': tk.StringVar(value='1'),
            'zoom_label': None,
            'canvas': None,
            'cv': None,
            'total_pages': 0,
            'show_char_bbox_var': tk.BooleanVar(value=False),
            'show_glyph_bbox_var': tk.BooleanVar(value=False),  # 添加字形边界显示控制变量
            'show_bbox_info_var': tk.BooleanVar(value=True),  # 添加框线坐标显示控制变量(默认显示)
            'show_spacing_info_var': tk.BooleanVar(value=True),  # 添加段前/段后距离显示控制变量(默认显示)
            'show_baseline_var': tk.BooleanVar(value=False),  # 添加基线显示控制变量
            'scale_var': tk.StringVar(value="100%")  # 预先创建缩放百分比StringVar
        }
        
        # 保存窗口和状态
        self.preview_windows[pdf_path] = preview
        self.preview_states[pdf_path] = state
        
        # 设置窗口关闭时的处理函数
        def on_closing():
            if pdf_path in self.preview_states:
                state = self.preview_states[pdf_path]
                if state['cv']:
                    state['cv'].close()
                del self.preview_states[pdf_path]
            if pdf_path in self.preview_windows:
                del self.preview_windows[pdf_path]
            preview.destroy()
        
        preview.protocol("WM_DELETE_WINDOW", on_closing)
        
        return preview, state
    
    def _parse_document(self, pdf_path, state):
        '''Parse the PDF document and prepare it for preview'''
        # 创建转换器
        print(f"Opening PDF file: {pdf_path}")
        state['cv'] = Converter(pdf_path)
        state['cv'].load_pages()
        
        # 解析文档
        print("开始解析文档...")
        
        # 获取默认设置并启用调试
        parse_settings = state['cv'].default_settings
        parse_settings.update({
            'debug': True,  # 启用布局调试
            'raw_exceptions': True  # 显示详细错误信息
        })
        
        try:
            # 使用parse方法一次性完成所有解析步骤
            state['cv'].parse(**parse_settings)
            print("文档解析完成")
            
            # 打印sections信息
            for page_idx, page in enumerate(state['cv'].pages):
                if not page.skip_parsing:
                    print(f"\nPage {page_idx + 1} sections info:")
                    print(f"Number of sections: {len(page.sections)}")
                    for i, section in enumerate(page.sections):
                        print(f"  Section {i+1}: {section.bbox}")
        except Exception as parse_error:
            print(f"Error parsing document: {str(parse_error)}")
            import traceback
            traceback.print_exc()
            raise
    
    def _setup_preview_controls(self, preview, pdf_path, state):
        '''Setup the control panel and display the first page'''
        # 创建控制面板
        control_frame = tk.Frame(preview, bg='#f0f0f0')
        control_frame.pack(side=tk.TOP, fill=tk.X)
        
        # 添加页面导航按钮
        prev_button = tk.Button(control_frame, text="上一页", 
                              command=lambda p=pdf_path: self.change_page(-1, p),
                              font=("Helvetica", 10),
                              bg="#e0e0e0")
        prev_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 页面计数器
        page_label = tk.Label(control_frame, textvariable=state['current_page'],
                            font=("Helvetica", 10),
                            bg='#f0f0f0')
        page_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 总页数
        state['total_pages'] = len(state['cv'].pages)
        total_label = tk.Label(control_frame, text=f"/ {state['total_pages']}",
                             font=("Helvetica", 10),
                             bg='#f0f0f0')
        total_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 下一页按钮
        next_button = tk.Button(control_frame, text="下一页", 
                              command=lambda p=pdf_path: self.change_page(1, p),
                              font=("Helvetica", 10),
                              bg="#e0e0e0")
        next_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 缩放控制 - 完全重构，不依赖change_zoom函数
        def zoom_out_callback(p=pdf_path):
            # 直接在回调中处理所有缩放逻辑
            current_state = self.preview_states[p]
            
            # 更新缩放比例
            current_state['zoom_scale'] = max(0.1, current_state['zoom_scale'] - 0.1)
            zoom_percentage = f"{int(current_state['zoom_scale'] * 100)}%"
            
            # 立即更新显示
            current_state['scale_var'].set(zoom_percentage)
            
            # 强制刷新界面
            preview.update_idletasks()
            
            # 重新显示当前页面
            current = int(current_state['current_page'].get())
            self.display_page(current - 1, p)
        
        zoom_out_button = tk.Button(control_frame, text="缩小", 
                                  command=zoom_out_callback,
                                  font=("Helvetica", 10),
                                  bg="#e0e0e0")
        zoom_out_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 缩放百分比显示
        state['zoom_label'] = tk.Label(control_frame, 
                                textvariable=state['scale_var'],
                                font=("Helvetica", 10),
                                width=5,  # 固定宽度避免标签大小变化
                                bg='#f0f0f0')
        state['zoom_label'].pack(side=tk.LEFT, padx=5, pady=5)
        
        # 放大按钮 - 完全重构
        def zoom_in_callback(p=pdf_path):
            # 直接在回调中处理所有缩放逻辑
            current_state = self.preview_states[p]
            
            # 更新缩放比例
            current_state['zoom_scale'] = min(5.0, current_state['zoom_scale'] + 0.1)
            zoom_percentage = f"{int(current_state['zoom_scale'] * 100)}%"
            
            # 立即更新显示
            current_state['scale_var'].set(zoom_percentage)
            
            # 强制刷新界面
            preview.update_idletasks()
            
            # 重新显示当前页面
            current = int(current_state['current_page'].get())
            self.display_page(current - 1, p)
        
        zoom_in_button = tk.Button(control_frame, text="放大", 
                                 command=zoom_in_callback,
                                 font=("Helvetica", 10),
                                 bg="#e0e0e0")
        zoom_in_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示字符边界复选框
        state['show_char_bbox_var'] = tk.BooleanVar(value=False)
        show_char_bbox = tk.Checkbutton(control_frame, text="显示字符边界", 
                                       variable=state['show_char_bbox_var'], 
                                       command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                       font=("Helvetica", 10),
                                       bg='#f0f0f0')
        show_char_bbox.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示Line背景复选框
        state['show_line_bg_var'] = tk.BooleanVar(value=False)
        show_line_bg = tk.Checkbutton(control_frame, text="显示Line", 
                                     variable=state['show_line_bg_var'], 
                                     command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                     font=("Helvetica", 10),
                                     bg='#f0f0f0')
        show_line_bg.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示Span背景复选框
        state['show_span_bg_var'] = tk.BooleanVar(value=False)
        show_span_bg = tk.Checkbutton(control_frame, text="显示Span", 
                                     variable=state['show_span_bg_var'], 
                                     command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                     font=("Helvetica", 10),
                                     bg='#f0f0f0')
        show_span_bg.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示框线坐标值复选框
        state['show_bbox_info_var'] = tk.BooleanVar(value=True)  # 默认显示坐标
        show_bbox_info = tk.Checkbutton(control_frame, text="显示坐标", 
                                      variable=state['show_bbox_info_var'], 
                                      command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                      font=("Helvetica", 10),
                                      bg='#f0f0f0')
        show_bbox_info.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示段前/段后距离复选框
        state['show_spacing_info_var'] = tk.BooleanVar(value=True)  # 默认显示段间距
        show_spacing_info = tk.Checkbutton(control_frame, text="显示段间距", 
                                          variable=state['show_spacing_info_var'], 
                                          command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                          font=("Helvetica", 10),
                                          bg='#f0f0f0')
        show_spacing_info.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示投影数据复选框
        state['show_projection_var'] = tk.BooleanVar(value=False)
        show_projection = tk.Checkbutton(control_frame, text="显示投影", 
                                       variable=state['show_projection_var'], 
                                       command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                       font=("Helvetica", 10),
                                       bg='#f0f0f0')
        show_projection.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示基线复选框
        state['show_baseline_var'] = tk.BooleanVar(value=False)
        show_baseline = tk.Checkbutton(control_frame, text="显示基线",
                                     variable=state['show_baseline_var'],
                                     command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                     font=("Helvetica", 10),
                                     bg='#f0f0f0')
        show_baseline.pack(side=tk.LEFT, padx=5, pady=5)

        # 显示字形边界复选框
        state['show_glyph_bbox_var'] = tk.BooleanVar(value=False)
        show_glyph_bbox = tk.Checkbutton(control_frame, text="显示字形边界",
                                       variable=state['show_glyph_bbox_var'],
                                       command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                       font=("Helvetica", 10),
                                       bg='#f0f0f0')
        show_glyph_bbox.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 显示原始bbox复选框
        state['show_original_bbox_var'] = tk.BooleanVar(value=True)
        show_original_bbox = tk.Checkbutton(control_frame, text="显示原始框线", 
                                          variable=state['show_original_bbox_var'], 
                                          command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                                          font=("Helvetica", 10),
                                          bg='#f0f0f0')
        show_original_bbox.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 添加Line和Span背景互斥处理
        def toggle_line_bg():
            if state['show_line_bg_var'].get() and state['show_span_bg_var'].get():
                state['show_span_bg_var'].set(False)
            self.display_page(int(state['current_page'].get()) - 1, pdf_path)
            
        def toggle_span_bg():
            if state['show_span_bg_var'].get() and state['show_line_bg_var'].get():
                state['show_line_bg_var'].set(False)
            self.display_page(int(state['current_page'].get()) - 1, pdf_path)
        
        # 重新配置复选框命令，添加互斥逻辑
        show_line_bg.config(command=toggle_line_bg)
        show_span_bg.config(command=toggle_span_bg)
        
        # 创建画布框架
        canvas_frame = tk.Frame(preview, bg='#f0f0f0')
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient='horizontal')
        h_scrollbar.pack(side='bottom', fill='x')
        
        v_scrollbar = tk.Scrollbar(canvas_frame)
        v_scrollbar.pack(side='right', fill='y')
        
        state['canvas'] = tk.Canvas(canvas_frame, 
                         xscrollcommand=h_scrollbar.set,
                         yscrollcommand=v_scrollbar.set,
                         bg='white')  # 设置白色背景
        state['canvas'].pack(side='left', fill='both', expand=True)
        
        h_scrollbar.config(command=state['canvas'].xview)
        v_scrollbar.config(command=state['canvas'].yview)
        
        # 添加鼠标滚轮事件处理
        def on_mousewheel(event):
            try:
                if event.state & 0x4:  # 检查是否按下Ctrl键
                    # 按下Ctrl键时，实现缩放功能
                    delta = event.delta if hasattr(event, 'delta') else event.num
                    if delta > 0:
                        state['zoom_scale'] = min(5.0, state['zoom_scale'] + 0.1)
                    else:
                        state['zoom_scale'] = max(0.1, state['zoom_scale'] - 0.1)
                    
                    # 更新缩放标签
                    state['scale_var'].set(f"{int(state['zoom_scale'] * 100)}%")
                    
                    # 重新显示当前页面
                    current = int(state['current_page'].get())
                    self.display_page(current - 1, pdf_path)
                    return "break"  # 阻止事件继续传播
                else:
                    # 不按Ctrl时，实现正常的滚动
                    delta = event.delta if hasattr(event, 'delta') else (-1 if event.num == 4 else 1)
                    scroll_amount = int(-1 * (delta / 120))  # 计算滚动量
                    if abs(scroll_amount) > 0:  # 确保有实际的滚动
                        state['canvas'].yview_scroll(scroll_amount, "units")
            except Exception as e:
                print(f"Mouse wheel event error: {str(e)}")
        
        # 绑定鼠标滚轮事件
        state['canvas'].bind('<MouseWheel>', on_mousewheel)
        state['canvas'].bind('<Control-MouseWheel>', on_mousewheel)
        
        # 显示第一页
        self.display_page(0, pdf_path)
    
    def _callback_preview(self):
        '''Preview PDF pages with sections and columns highlighted'''
        # input check
        if not self.pdf_paths:
            messagebox.showerror('Error', 'Please select PDF file first.')
            return
        
        # 处理所有选择的文件
        pdf_paths_list = list(self.pdf_paths)
        
        for pdf_path in pdf_paths_list:
            # 检查文件是否已经打开
            if self._check_existing_preview(pdf_path):
                continue
            
            try:
                # 创建预览窗口和初始化状态
                preview, state = self._create_preview_window(pdf_path)
                
                # 解析文档
                self._parse_document(pdf_path, state)
                
                # 创建控制面板和显示第一页
                self._setup_preview_controls(preview, pdf_path, state)
                
            except Exception as e:
                messagebox.showerror('Error', f'Failed to preview PDF: {str(e)}')
                if pdf_path in self.preview_windows:
                    self.preview_windows[pdf_path].destroy()
                    del self.preview_windows[pdf_path]
                if pdf_path in self.preview_states:
                    del self.preview_states[pdf_path]
            
            # 添加鼠标滚轮事件处理
            def on_mousewheel(event, p=pdf_path):
                try:
                    if event.state & 0x4:  # 检查是否按下Ctrl键
                        # 按下Ctrl键时，实现缩放功能
                        delta = event.delta if hasattr(event, 'delta') else event.num
                        if delta > 0:
                            state['zoom_scale'] = min(5.0, state['zoom_scale'] + 0.1)
                        else:
                            state['zoom_scale'] = max(0.1, state['zoom_scale'] - 0.1)
                        
                        # 更新缩放标签
                        state['scale_var'].set(f"{int(state['zoom_scale'] * 100)}%")
                        
                        # 重新显示当前页面
                        current = int(state['current_page'].get())
                        self.display_page(current - 1, p)
                        return "break"  # 阻止事件继续传播
                    else:
                        # 不按Ctrl时，实现正常的滚动
                        delta = event.delta if hasattr(event, 'delta') else (-1 if event.num == 4 else 1)
                        scroll_amount = int(-1 * (delta / 120))  # 计算滚动量
                        if abs(scroll_amount) > 0:  # 确保有实际的滚动
                            state['canvas'].yview_scroll(scroll_amount, "units")
                except Exception as e:
                    print(f"Mouse wheel event error: {str(e)}")
            
            # 绑定鼠标滚轮事件
            state['canvas'].bind('<MouseWheel>', on_mousewheel)
            state['canvas'].bind('<Control-MouseWheel>', on_mousewheel)
            
            # 显示第一页
            self.display_page(0, pdf_path)
            
            # 窗口关闭时清理资源
            def on_closing(p=pdf_path):
                if p in self.preview_states:
                    state = self.preview_states[p]
                    if state['cv']:
                        state['cv'].close()
                    del self.preview_states[p]
                if p in self.preview_windows:
                    del self.preview_windows[p]
                preview.destroy()
            
            preview.protocol("WM_DELETE_WINDOW", on_closing)

    def change_page(self, delta, pdf_path):
        try:
            state = self.preview_states[pdf_path]
            new_page = int(state['current_page'].get()) + delta
            if 1 <= new_page <= state['total_pages']:
                state['current_page'].set(str(new_page))
                self.display_page(new_page - 1, pdf_path)
        except ValueError:
            pass

    def change_zoom(self, delta, pdf_path):
        # 获取状态字典
        state = self.preview_states[pdf_path]
        
        # 更新缩放比例
        old_zoom = state['zoom_scale']
        state['zoom_scale'] = max(0.1, min(5.0, state['zoom_scale'] + delta))
        
        # 计算新的缩放百分比
        zoom_percentage = f"{int(state['zoom_scale'] * 100)}%"
        
        # 更新显示 - 使用多种方式确保显示更新
        try:
            # 更新StringVar
            if 'scale_var' in state:
                state['scale_var'].set(zoom_percentage)
            
            # 直接更新标签文本
            if 'zoom_label' in state:
                state['zoom_label'].config(text=zoom_percentage)
                # 强制更新界面
                state['zoom_label'].update()
            
            # 强制更新父窗口
            if pdf_path in self.preview_windows:
                self.preview_windows[pdf_path].update_idletasks()
        except Exception as e:
            print(f"Error updating zoom display: {str(e)}")
        
        # 重新显示当前页面以应用新的缩放级别
        current = int(state['current_page'].get())
        self.display_page(current - 1, pdf_path)

    def display_page(self, page_idx, pdf_path):
        # 获取窗口状态
        state = self.preview_states[pdf_path]
        
        # 清除当前画布内容
        state['canvas'].delete('all')
        
        try:
            # 获取PDF页面并渲染
            doc = fitz.open(pdf_path)  # 打开PDF文件
            page = doc[page_idx]  # 获取指定页面
            
            # 应用Element.ROTATION_MATRIX以及缩放比例
            # 先获取旋转矩阵，再结合缩放矩阵
            rotation_matrix = fitz.Matrix()
            if page_idx < len(state['cv'].pages):
                rotation_matrix = state['cv'].pages[page_idx]._rotation_matrix

            scale_matrix = fitz.Matrix(state['zoom_scale'], state['zoom_scale'])
            # 组合旋转和缩放矩阵
            combined_matrix = rotation_matrix * scale_matrix
            
            # 使用组合后的矩阵渲染页面
            pix = page.get_pixmap(matrix=combined_matrix)
            
            # 创建PIL图像
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            draw = ImageDraw.Draw(img)
            
            # 获取页面大小
            x0, y0 = 0, 0
            x1, y1 = pix.width - 1, pix.height - 1  # 使用图像的实际像素大小
            
            try:
                # 创建字体对象，为尺寸信息使用固定大小的字体
                zoom_font = ImageFont.truetype("arial.ttf", int(12 * state['zoom_scale']))  # 用于缩放的元素
                fixed_font = ImageFont.truetype("arial.ttf", 12)  # 固定大小的字体用于尺寸信息
            except Exception as font_error:
                print(f"Warning: Could not load Arial font: {str(font_error)}")
                # 使用默认字体
                zoom_font = ImageFont.load_default()
                fixed_font = ImageFont.load_default()
            
            # 绘制最外层边框（粉色）
            border_width = 1  # 固定边框宽度为1像素
            border_color = (255, 192, 203)  # 粉色
            draw.rectangle([x0, y0, x1, y1], outline=border_color, width=border_width)
            
            # 绘制页面尺寸信息，使用固定大小，只有在显示坐标复选框被勾选时才显示
            if state['show_bbox_info_var'].get():
                self.draw_bbox_info(draw, [x0, y0, x1, y1], (0, 0, 0), fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
            
            # 如果页面已解析，绘制sections和columns的边框
            if page_idx < len(state['cv'].pages):
                pdf_page = state['cv'].pages[page_idx]
                if not pdf_page.skip_parsing and hasattr(pdf_page, 'sections'):
                    print(f"Drawing layout for page {page_idx + 1}")
                    print(f"Found {len(pdf_page.sections)} sections")
                    
                    # 先绘制sections（红色，最细）
                    for section in pdf_page.sections:
                        try:
                            x0, y0, x1, y1 = section.bbox
                            x0, y0, x1, y1 = [coord * state['zoom_scale'] for coord in [x0, y0, x1, y1]]
                            section_width = 1  # 固定边框宽度为1像素
                            draw.rectangle([x0, y0, x1, y1], outline=(255, 0, 0), width=section_width)
                            # 绘制section尺寸信息，使用固定大小
                            if state['show_bbox_info_var'].get():
                                self.draw_bbox_info(draw, [x0, y0, x1, y1], (255, 0, 0), fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                        except Exception as section_error:
                            print(f"Warning: Could not draw section: {str(section_error)}")
                    
                    
                    
                    # 再绘制columns（绿色）和它们包含的blocks
                    for section in pdf_page.sections:
                        if hasattr(section, '__iter__'):
                            for column in section:
                                try:
                                    # 绘制column（中等粗细）
                                    x0, y0, x1, y1 = column.bbox
                                    x0, y0, x1, y1 = [coord * state['zoom_scale'] for coord in [x0, y0, x1, y1]]
                                    column_width = 1  # 固定线条宽度为1像素
                                    draw.rectangle([x0, y0, x1, y1], outline=(0, 255, 0), width=column_width)
                                    # 绘制column尺寸信息，使用固定大小
                                    if state['show_bbox_info_var'].get():
                                        self.draw_bbox_info(draw, [x0, y0, x1, y1], (0, 255, 0), fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                    
                                    # 绘制该column中的blocks，根据类型使用不同的线条样式
                                    if hasattr(column, 'blocks'):
                                        for block in column.blocks:
                                            try:
                                                x0, y0, x1, y1 = block.bbox
                                                x0, y0, x1, y1 = [coord * state['zoom_scale'] for coord in [x0, y0, x1, y1]]
                                                block_width = 1  # 固定线条宽度为1像素
                                                dash_length = 5  # 固定虚线长度
                                                gap_length = 3  # 固定间隔长度
                                                
                                                # 根据block类型设置不同的线条样式和颜色
                                                if isinstance(block, ImageBlock):
                                                    color = (0, 0, 255)  # 蓝色
                                                    draw.rectangle([x0, y0, x1, y1], outline=color, width=block_width)
                                                    if state['show_bbox_info_var'].get():
                                                        self.draw_bbox_info(draw, [x0, y0, x1, y1], color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                elif isinstance(block, TableBlock):
                                                    color = (255, 165, 0)  # 橙色
                                                    # 绘制表格外边框
                                                    self.draw_dashed_rectangle(draw, [x0, y0, x1, y1], color, dash_length, gap_length, block_width)
                                                    # 绘制尺寸信息，使用固定大小
                                                    if state['show_bbox_info_var'].get():
                                                        self.draw_bbox_info(draw, [x0, y0, x1, y1], color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                    
                                                    # 绘制表格的每个单元格边框
                                                    cell_color = (255, 255, 128)  # 浅黄色
                                                    cell_width = 1  # 固定线条宽度为1像素
                                                    cell_dash = 2  # 固定虚线长度
                                                    cell_gap = 2  # 固定间隔长度
                                                    
                                                    # 为单元格使用固定大小的字体
                                                    try:
                                                        cell_font = fixed_font
                                                    except:
                                                        cell_font = fixed_font  # 如果创建失败，使用默认字体
                                                    
                                                    # 遍历所有行和单元格
                                                    for row in block._rows:
                                                        for cell in row:
                                                            if cell:  # 确保单元格存在
                                                                # 获取单元格坐标并应用缩放
                                                                cx0, cy0, cx1, cy1 = cell.bbox
                                                                cx0, cy0, cx1, cy1 = [coord * state['zoom_scale'] for coord in [cx0, cy0, cx1, cy1]]
                                                                # 绘制单元格边框
                                                                self.draw_dashed_rectangle(draw, [cx0, cy0, cx1, cy1], cell_color, cell_dash, cell_gap, cell_width)
                                                                # 绘制单元格尺寸信息，使用固定大小
                                                                if state['show_bbox_info_var'].get():
                                                                    self.draw_bbox_info(draw, [cx0, cy0, cx1, cy1], (102, 51, 0), cell_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                elif isinstance(block, TextBlock) or isinstance(block, TextFloatBlock):
                                                    if isinstance(block, TocBlock):
                                                        color = (75, 0, 130)  # 靛蓝色用于目录块
                                                        self.draw_dashed_rectangle(draw, [x0, y0, x1, y1], color, dash_length, gap_length, block_width)
                                                    elif isinstance(block, TextFloatBlock):
                                                        original_block = block  # 保存原始的TextFloatBlock引用
                                                        block = block.real_instance
                                                        
                                                        # 互斥显示逻辑：勾选时显示原始bbox，取消勾选时显示旋转后的bbox
                                                        if state['show_original_bbox_var'].get() and hasattr(original_block, 'original_bbox') and original_block.original_bbox:
                                                            # 显示原始bbox（旋转前的框线）
                                                            orig_x0, orig_y0, orig_x1, orig_y1 = original_block.original_bbox
                                                            orig_x0, orig_y0, orig_x1, orig_y1 = [coord * state['zoom_scale'] for coord in [orig_x0, orig_y0, orig_x1, orig_y1]]
                                                            original_color = (255, 20, 147)  # 深粉色用于原始bbox
                                                            # 使用虚线绘制原始bbox
                                                            self.draw_dashed_rectangle(draw, [orig_x0, orig_y0, orig_x1, orig_y1], original_color, dash_length, gap_length, block_width)
                                                            # 绘制原始bbox的尺寸信息
                                                            if state['show_bbox_info_var'].get():
                                                                self.draw_bbox_info(draw, [orig_x0, orig_y0, orig_x1, orig_y1], original_color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                        else:
                                                            # 显示旋转后的bbox（当前框线）
                                                            color = (128, 0, 128)  # 紫色用于浮动文本块
                                                            draw.rectangle([x0, y0, x1, y1], outline=color, width=block_width)
                                                            # 绘制旋转后bbox的尺寸信息
                                                            if state['show_bbox_info_var'].get():
                                                                self.draw_bbox_info(draw, [x0, y0, x1, y1], color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                    else:
                                                        color = (128, 0, 0)  # 深红色用于普通文本块
                                                        self.draw_dashed_rectangle(draw, [x0, y0, x1, y1], color, dash_length, gap_length, block_width)
                                                    
                                                    # 绘制尺寸信息
                                                    if state['show_bbox_info_var'].get():
                                                        self.draw_bbox_info(draw, [x0, y0, x1, y1], color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                                    # 绘制 spacing 信息
                                                    if state['show_spacing_info_var'].get():
                                                        self.draw_spacing_info(draw, block, [x0, y0, x1, y1], color, fixed_font)
                                                    
                                                    # 绘制Line背景颜色（交替）
                                                    if state['show_line_bg_var'].get() and not state['show_span_bg_var'].get():
                                                        # 定义两种交替的背景颜色（透明度为64，更透明）
                                                        line_bg_colors = [(255, 200, 200, 64), (200, 255, 200, 64)]  # 浅红和浅绿，更透明
                                                        
                                                        # 遍历每一行，使用交替的背景颜色
                                                        for line_idx, line in enumerate(block.lines):
                                                            try:
                                                                # 获取行的边界框并应用缩放
                                                                lx0, ly0, lx1, ly1 = line.bbox
                                                                lx0, ly0, lx1, ly1 = [coord * state['zoom_scale'] for coord in [lx0, ly0, lx1, ly1]]
                                                                # 使用交替的背景颜色
                                                                line_bg_color = line_bg_colors[line_idx % 2]
                                                                # 绘制行背景
                                                                draw.rectangle([lx0, ly0, lx1, ly1], fill=line_bg_color, outline=None)
                                                                 
                                                                # 如果启用了基线显示且是TextBlock或TextFloatBlock（非TocBlock和其他Block），绘制行的基线位置
                                                                if state['show_baseline_var'].get() and (isinstance(block, TextBlock) and not isinstance(block, TocBlock) or isinstance(block, TextFloatBlock)):
                                                                    try:
                                                                        # 获取基线位置并应用缩放
                                                                        baseline = line.get_baseline() * state['zoom_scale']
                                                                        if hasattr(line, 'is_vertical_text') and line.is_vertical_text:
                                                                            # 对于垂直文本，绘制垂直基线 - 使用红色
                                                                            baseline_x = baseline
                                                                            draw.line([(baseline_x, ly0), (baseline_x, ly1)], 
                                                                                    fill=(255, 0, 0),  # 红色
                                                                                    width=2)  # 固定宽度为2
                                                                            # 在基线上方显示基线值
                                                                            baseline_text = f"BL: {line.get_baseline():.1f} (V)"
                                                                            draw.text((baseline_x - 30, ly0 - 20), baseline_text, fill=(0, 0, 255), font=fixed_font)
                                                                        else:
                                                                            # 对于水平文本，绘制水平基线 - 使用红色
                                                                            draw.line([(lx0, baseline), (lx1, baseline)], fill=(255, 0, 0), width=2)
                                                                            # 在基线左侧显示基线值
                                                                            baseline_text = f"BL: {line.get_baseline():.1f} (H)"
                                                                            draw.text((lx0 - 65, baseline - 7), baseline_text, fill=(255, 0, 0), font=fixed_font)
                                                                    except Exception as baseline_error:
                                                                        print(f"Warning: Could not draw baseline: {str(baseline_error)}")
                                                            except Exception as line_error:
                                                                print(f"Warning: Could not draw line background: {str(line_error)}")
                                                    
                                                    # 当启用基线显示但不显示背景时，只为TextBlock和TextFloatBlock（非TocBlock和其他Block）绘制基线位置
                                                    if not state['show_line_bg_var'].get() and state['show_baseline_var'].get() and (isinstance(block, TextBlock) and not isinstance(block, TocBlock) or isinstance(block, TextFloatBlock)):
                                                        # 遍历每一行
                                                        for line_idx, line in enumerate(block.lines):
                                                            try:
                                                                # 获取行的边界框并应用缩放
                                                                lx0, ly0, lx1, ly1 = line.bbox
                                                                lx0, ly0, lx1, ly1 = [coord * state['zoom_scale'] for coord in [lx0, ly0, lx1, ly1]]
                                                                
                                                                # 获取基线位置并应用缩放
                                                                baseline = line.get_baseline() * state['zoom_scale']
                                                                if hasattr(line, 'is_vertical_text') and line.is_vertical_text:
                                                                    # 对于垂直文本，绘制垂直基线 - 使用红色
                                                                    baseline_x = baseline
                                                                    draw.line([(baseline_x, ly0), (baseline_x, ly1)], fill=(255, 0, 0), width=2)
                                                                    # 在基线上方显示基线值
                                                                    baseline_text = f"BL: {line.get_baseline():.1f} (V)"
                                                                    draw.text((baseline_x - 30, ly0 - 20), baseline_text, fill=(0, 0, 255), font=fixed_font)
                                                                else:
                                                                    # 对于水平文本，绘制水平基线 - 使用红色
                                                                    draw.line([(lx0, baseline), (lx1, baseline)], fill=(255, 0, 0), width=2)
                                                                    # 在基线左侧显示基线值
                                                                    baseline_text = f"BL: {line.get_baseline():.1f} (H)"
                                                                    draw.text((lx0 - 65, baseline - 7), baseline_text, fill=(255, 0, 0), font=fixed_font)
                                                            except Exception as baseline_error:
                                                                print(f"Warning: Could not draw baseline: {str(baseline_error)}")
                                                    
                                                    # 绘制Span背景颜色（交替）
                                                    if state['show_span_bg_var'].get() and not state['show_line_bg_var'].get():
                                                        # 定义两种交替的背景颜色（透明度为64，更透明）
                                                        span_bg_colors = [(200, 200, 255, 64), (255, 255, 200, 64)]  # 浅蓝和浅黄，更透明
                                                        
                                                        # 首先收集整个block中的所有span
                                                        all_spans = []
                                                        for line in block.lines:
                                                            all_spans.extend(line.spans)
                                                        
                                                        # 然后按照在整个block中的顺序绘制所有span的背景
                                                        for span_idx, span in enumerate(all_spans):
                                                            try:
                                                                # 获取span的边界框并应用缩放
                                                                sx0, sy0, sx1, sy1 = span.bbox
                                                                sx0, sy0, sx1, sy1 = [coord * state['zoom_scale'] for coord in [sx0, sy0, sx1, sy1]]
                                                                # 使用交替的背景颜色
                                                                span_bg_color = span_bg_colors[span_idx % 2]
                                                                # 绘制span背景
                                                                draw.rectangle([sx0, sy0, sx1, sy1], fill=span_bg_color, outline=None)
                                                            except Exception as span_error:
                                                                print(f"Warning: Could not draw span background: {str(span_error)}")
                                                    
                                                    # 绘制每个字符的边界框
                                                    char_color = (0, 0, 255)  # 蓝色
                                                    char_width = 1  # 字符边框宽度固定为1像素，不随缩放而变化

                                                    # 绘制字形边界框
                                                    glyph_color = (255, 165, 0)  # 橙色
                                                    glyph_width = 1  # 字形边框宽度固定为1像素，不随缩放而变化

                                                    # 遍历每一行
                                                    if state['show_char_bbox_var'].get() or state['show_glyph_bbox_var'].get():
                                                        for line in block.lines:
                                                            # 遍历每个span
                                                            for span in line.spans:
                                                                # 遍历每个字符
                                                                for char in span.chars:
                                                                    try:
                                                                        # 获取字符的边界框并应用缩放
                                                                        cx0, cy0, cx1, cy1 = char.bbox
                                                                        cx0, cy0, cx1, cy1 = [coord * state['zoom_scale'] for coord in [cx0, cy0, cx1, cy1]]

                                                                        # 绘制字符边界框
                                                                        if state['show_char_bbox_var'].get():
                                                                            draw.rectangle([cx0, cy0, cx1, cy1], outline=char_color, width=char_width)

                                                                        # 绘制字形边界框
                                                                        if state['show_glyph_bbox_var'].get():
                                                                            glyph_bbox = self.get_glyph_bbox(char, page_idx, pdf_path)
                                                                            if glyph_bbox:
                                                                                gx0, gy0, gx1, gy1 = glyph_bbox
                                                                                gx0, gy0, gx1, gy1 = [coord * state['zoom_scale'] for coord in [gx0, gy0, gx1, gy1]]
                                                                                draw.rectangle([gx0, gy0, gx1, gy1], outline=glyph_color, width=glyph_width)
                                                                        
                                                                        # 再绘制origin线
                                                                        if char.origin:
                                                                            # 获取origin点并应用缩放
                                                                            ox, oy = char.origin
                                                                            ox, oy = [coord * state['zoom_scale'] for coord in [ox, oy]]
                                                                            
                                                                            # 检查字符所属的span和line的文本方向
                                                                            is_vertical = False
                                                                            if hasattr(line, 'text_direction'):
                                                                                is_vertical = (line.text_direction == TextDirection.BOTTOM_TOP)
                                                                            elif hasattr(line, 'is_vertical_text'):
                                                                                is_vertical = line.is_vertical_text
                                                                            
                                                                            # 绘制字符最高点线（ascender线）
                                                                            if hasattr(span, 'ascender') and hasattr(span, 'size'):
                                                                                # 计算字符最高点位置：origin - ascender * 字体大小
                                                                                ascender_height = oy - (span.ascender * span.size * state['zoom_scale'])
                                                                                
                                                                                # if not is_vertical:
                                                                                #      # 水平文本 - 绘制水平字符最高点线, 垂直文本暂时不绘制
                                                                                #     draw.line([(cx0, ascender_height), (cx1, ascender_height)], 
                                                                                #             fill=(227, 197, 154),
                                                                                #             width=1)  # 固定宽度为1
           
                                                                            if is_vertical:
                                                                                # 垂直文本 - 绘制垂直线（红色）
                                                                                center_x = (cx0 + cx1) / 2  # 字符bbox的中心x坐标
                                                                                draw.line([(center_x, cy0), (center_x, cy1)], 
                                                                                        fill=(255, 0, 0),  # 红色
                                                                                        width=1)  # 固定宽度为1
                                                                            else:
                                                                                # 水平文本 - 绘制水平线（红色）
                                                                                line_width = int(cx1 - cx0)  # 使用字符的宽度
                                                                                draw.line([(ox, oy), (ox+line_width, oy)], 
                                                                                        fill=(255, 0, 0),  # 红色
                                                                                        width=1)  # 固定高度为1

                                                                    except Exception as char_error:
                                                                        print(f"Warning: Could not draw char bbox: {str(char_error)}")
                                                else:
                                                    color = (128, 0, 128)  # 紫色
                                                    # 绘制文本块边框
                                                    self.draw_dashed_rectangle(draw, [x0, y0, x1, y1], color, dash_length, gap_length, block_width)
                                                    # 绘制尺寸信息
                                                    if state['show_bbox_info_var'].get():
                                                        self.draw_bbox_info(draw, [x0, y0, x1, y1], color, fixed_font, use_zoom=False, zoom_scale=state['zoom_scale'])
                                            except Exception as block_error:
                                                print(f"Warning: Could not draw block: {str(block_error)}")
                                except Exception as column_error:
                                    print(f"Warning: Could not draw column: {str(column_error)}")
                    # 绘制投影数据（黑色）
                    if pdf_page.gaps and state['show_projection_var'].get():
                        try:
                            print(f"Drawing projection data for page {page_idx + 1}")
                            
                            # 根据页面的写作模式确定投影方向
                            is_horizontal = True
                            if hasattr(pdf_page, 'is_horizontal_write_mode_page'):
                                is_horizontal = pdf_page.is_horizontal_write_mode_page()
                            
                            # 获取页面的垂直或水平范围（根据写作模式）
                            if is_horizontal:
                                # 水平版面，使用垂直范围
                                _, page_y0, _, page_y1 = pdf_page.bbox
                                page_y0 *= state['zoom_scale']
                                page_y1 *= state['zoom_scale']
                                # 页面的水平范围（用于垂直版面）
                                page_x0, _, page_x1, _ = pdf_page.bbox
                                page_x0 *= state['zoom_scale']
                                page_x1 *= state['zoom_scale']
                            else:
                                # 垂直版面，使用水平范围
                                page_x0, _, page_x1, _ = pdf_page.bbox
                                page_x0 *= state['zoom_scale']
                                page_x1 *= state['zoom_scale']
                                # 页面的垂直范围（用于水平版面）
                                _, page_y0, _, page_y1 = pdf_page.bbox
                                page_y0 *= state['zoom_scale']
                                page_y1 *= state['zoom_scale']

                            projection_color = (0, 0, 0)  # 黑色
                            
                            # 绘制每个空白间隙
                            for gap_data in pdf_page.gaps:
                                if len(gap_data) >= 3:  # 确保数据格式正确 (x1, x2, value)
                                    # 获取基本信息
                                    gap_x1, gap_x2, value = gap_data[0], gap_data[1], gap_data[2]
                                    
                                    # 缩放坐标 (注意: 对于水平版面，这是x轴坐标；对于垂直版面，这是y轴坐标)
                                    gap_x1 *= state['zoom_scale']
                                    gap_x2 *= state['zoom_scale']
                                    
                                    # 计算投影区间的宽度
                                    gap_width = int(gap_x2 - gap_x1)
                                    if gap_width < 1:  # 确保最小宽度为1像素
                                        gap_width = 1
                                    
                                    # 检查是否有垂直范围信息（第4个值）
                                    if len(gap_data) >= 4 and gap_data[3]:
                                        # 使用指定的垂直范围列表
                                        y_ranges = gap_data[3]
                                        
                                        # 绘制每个垂直范围的投影区域
                                        for y_range in y_ranges:
                                            if len(y_range) == 2:  # 确保垂直范围格式正确 (y0, y1)
                                                range_y0, range_y1 = y_range
                                                # 缩放垂直坐标
                                                range_y0 *= state['zoom_scale']
                                                range_y1 *= state['zoom_scale']
                                                
                                                # 根据页面写作模式绘制矩形覆盖投影区间的指定范围
                                                if is_horizontal:
                                                    # 水平版面 - 垂直投影 (x轴上的投影)
                                                    # gap_x1,gap_x2 是x轴的投影坐标，range_y0,range_y1是y轴上的影响范围
                                                    draw.rectangle([
                                                        gap_x1, range_y0,
                                                        gap_x2, range_y1
                                                    ], 
                                                    fill=projection_color,
                                                    outline=None)
                                                else:
                                                    # 垂直版面 - 水平投影 (y轴上的投影)
                                                    # gap_x1,gap_x2 是y轴的投影坐标，range_y0,range_y1是x轴上的影响范围
                                                    # 在垂直版面中，我们需要绘制水平条形，因此：
                                                    # x轴范围: range_y0 到 range_y1
                                                    # y轴范围: gap_x1 到 gap_x2
                                                    draw.rectangle([
                                                        range_y0, gap_x1,
                                                        range_y1, gap_x2
                                                    ], 
                                                    fill=projection_color,
                                                    outline=None)
                                                
                                                # 在每个投影区域添加信息
                                                if is_horizontal:
                                                    gap_text = f"X Gap: {gap_x1/state['zoom_scale']:.1f}-{gap_x2/state['zoom_scale']:.1f}"
                                                else:
                                                    gap_text = f"Y Gap: {gap_x1/state['zoom_scale']:.1f}-{gap_x2/state['zoom_scale']:.1f}"
                                                value_text = f"Value: {value}"
                                                if is_horizontal:
                                                    range_text = f"Y Range: {range_y0/state['zoom_scale']:.1f}-{range_y1/state['zoom_scale']:.1f}"
                                                else:
                                                    range_text = f"X Range: {range_y0/state['zoom_scale']:.1f}-{range_y1/state['zoom_scale']:.1f}"
                                                
                                                # 使用绿色文字以便于在黑色背景上显示
                                                # 显示间隙范围、value值和范围
                                                if is_horizontal:
                                                    # 水平版面 - 文本显示在垂直投影顶部
                                                    draw.text((gap_x1 + 5, range_y0 + 10), 
                                                             gap_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                                    draw.text((gap_x1 + 5, range_y0 + 30), 
                                                             value_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                                    draw.text((gap_x1 + 5, range_y0 + 50), 
                                                             range_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                                else:
                                                    # 垂直版面 - 文本显示在水平投影左侧
                                                    # 对于垂直版面中的水平投影，文本位于条形左上角
                                                    text_x = range_y0 + 5   # x轴位置（条形边缘之后偏移5像素）
                                                    text_y = gap_x1 + 1    # 条形上方
                                                    
                                                    draw.text((text_x, text_y), 
                                                             gap_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                                    draw.text((text_x, text_y + 20), 
                                                             value_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                                    draw.text((text_x, text_y + 40), 
                                                             range_text, 
                                                             fill=(0, 255, 0), 
                                                             font=fixed_font)
                                    else:
                                        # 如果没有范围信息，根据页面写作模式使用整个页面高度或宽度
                                        if is_horizontal:
                                            # 水平版面 - 绘制垂直投影（原有行为）
                                            # gap_x1,gap_x2 是x轴的投影坐标，使用整个页面高度作为y轴范围
                                            draw.rectangle([
                                                gap_x1, page_y0,
                                                gap_x2, page_y1
                                            ], 
                                            fill=projection_color,
                                            outline=None)
                                        else:
                                            # 垂直版面 - 绘制水平投影
                                            # gap_x1,gap_x2 是y轴的投影坐标，使用整个页面宽度作为x轴范围
                                            # 对于垂直版面，我们使用页面宽度作为x轴范围，投影坐标作为y轴范围
                                            draw.rectangle([
                                                page_x0, gap_x1,
                                                page_x1, gap_x2
                                            ], 
                                            fill=projection_color,
                                            outline=None)
                                        
                                        # 在投影区域添加信息
                                        if is_horizontal:
                                            gap_text = f"X Gap: {gap_x1/state['zoom_scale']:.1f}-{gap_x2/state['zoom_scale']:.1f}"
                                        else:
                                            gap_text = f"Y Gap: {gap_x1/state['zoom_scale']:.1f}-{gap_x2/state['zoom_scale']:.1f}"
                                        value_text = f"Value: {value}"
                                        
                                        # 使用绿色文字以便于在黑色背景上显示
                                        if is_horizontal:
                                            # 水平版面 - 文本显示在垂直投影顶部
                                            draw.text((gap_x1 + 5, page_y0 + 10), 
                                                     gap_text, 
                                                     fill=(0, 255, 0), 
                                                     font=fixed_font)
                                            draw.text((gap_x1 + 5, page_y0 + 30), 
                                                     value_text, 
                                                     fill=(0, 255, 0), 
                                                     font=fixed_font)
                                        else:
                                            # 垂直版面 - 文本显示在水平投影上方
                                            # 对于垂直版面中的水平投影，文本位于条形上方
                                            text_x = page_x0 + 10   # x轴位置（页面左侧之后偏移10像素）
                                            text_y = gap_x1 + 1    # 条形上方
                                            
                                            draw.text((text_x, text_y), 
                                                     gap_text, 
                                                     fill=(0, 255, 0), 
                                                     font=fixed_font)
                                            draw.text((text_x, text_y + 20), 
                                                     value_text, 
                                                     fill=(0, 255, 0), 
                                                     font=fixed_font)
                        except Exception as projection_error:
                            print(f"Warning: Could not draw projection data: {str(projection_error)}")
            else:
                print("No sections found in page or page skipped")
            
            # 转换为PhotoImage并显示
            photo = ImageTk.PhotoImage(img)
            state['canvas'].create_image(0, 0, image=photo, anchor='nw')
            state['canvas'].image = photo  # 保持引用
            
            # 配置画布滚动区域
            state['canvas'].configure(scrollregion=state['canvas'].bbox('all'))
            
            # 关闭文档
            doc.close()
            
        except Exception as e:
            print(f"Error displaying page: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror('Error', f'Failed to display page {page_idx + 1}')
