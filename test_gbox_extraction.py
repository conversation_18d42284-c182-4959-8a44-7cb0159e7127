#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试gbox信息提取
"""

import fitz
import sys
import os

def test_gbox_extraction(pdf_path):
    """测试gbox信息提取"""
    print(f"测试PDF文件: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"错误: 文件不存在 {pdf_path}")
        return False
    
    try:
        doc = fitz.open(pdf_path)
        page = doc[0]  # 测试第一页
        
        print(f"页面尺寸: {page.rect}")
        
        # 获取rawdict格式的文本信息，启用准确边界框
        raw_dict = page.get_text("rawdict", flags=fitz.TEXT_ACCURATE_BBOXES)
        print(f"文本块数量: {len(raw_dict['blocks'])}")

        # 也测试不带标志的版本
        raw_dict_normal = page.get_text("rawdict")
        print(f"普通rawdict文本块数量: {len(raw_dict_normal['blocks'])}")
        
        # 检查字符是否包含gbox信息
        char_count = 0
        gbox_count = 0
        
        for block in raw_dict['blocks']:
            if block['type'] == 0:  # 文本块
                for line in block['lines']:
                    for span in line['spans']:
                        for char in span.get('chars', []):
                            char_count += 1
                            
                            if char_count <= 10:  # 只显示前10个字符的信息
                                print(f"\n字符 {char_count}:")
                                print(f"  字符: '{char['c']}'")
                                print(f"  所有字段: {list(char.keys())}")
                                print(f"  bbox: {char['bbox']}")
                                print(f"  origin: {char.get('origin', 'N/A')}")

                                # 检查是否有其他可能的字形边界字段
                                for key in char.keys():
                                    if 'glyph' in key.lower() or 'gbox' in key.lower():
                                        print(f"  {key}: {char[key]}")

                            # 检查是否有gbox信息
                            if 'gbox' in char:
                                gbox_count += 1

                                if char_count <= 10:
                                    print(f"  gbox: {char['gbox']}")

                                    # 比较bbox和gbox的差异
                                    bbox = char['bbox']
                                    gbox = char['gbox']
                                    if bbox and gbox:
                                        bbox_width = bbox[2] - bbox[0]
                                        bbox_height = bbox[3] - bbox[1]
                                        gbox_width = gbox[2] - gbox[0]
                                        gbox_height = gbox[3] - gbox[1]

                                        print(f"  bbox尺寸: {bbox_width:.2f} x {bbox_height:.2f}")
                                        print(f"  gbox尺寸: {gbox_width:.2f} x {gbox_height:.2f}")
                                        print(f"  尺寸差异: {bbox_width-gbox_width:.2f} x {bbox_height-gbox_height:.2f}")
        
        print(f"\n总字符数: {char_count}")
        print(f"包含gbox的字符数: {gbox_count}")
        print(f"gbox覆盖率: {gbox_count/char_count*100:.1f}%" if char_count > 0 else "gbox覆盖率: 0%")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 使用命令行参数或默认文件
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        # 查找当前目录下的PDF文件
        pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
        if pdf_files:
            pdf_path = pdf_files[0]
            print(f"使用找到的PDF文件: {pdf_path}")
        else:
            # 使用测试样本
            pdf_path = "test/samples/demo-text.pdf"
            if not os.path.exists(pdf_path):
                print("请提供PDF文件路径作为参数，或在当前目录放置PDF文件")
                sys.exit(1)
    
    success = test_gbox_extraction(pdf_path)
    if success:
        print("\n✓ 测试完成")
    else:
        print("\n✗ 测试失败")
        sys.exit(1)
