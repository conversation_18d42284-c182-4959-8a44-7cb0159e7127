#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字形边界框功能
"""

import fitz
import sys
import os

def test_glyph_bbox_extraction(pdf_path):
    """测试字形边界框提取功能"""
    print(f"测试PDF文件: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"错误: 文件不存在 {pdf_path}")
        return False
    
    try:
        doc = fitz.open(pdf_path)
        page = doc[0]  # 测试第一页
        
        print(f"页面尺寸: {page.rect}")
        
        # 获取文本信息
        text_dict = page.get_text("dict")
        print(f"文本块数量: {len(text_dict['blocks'])}")
        
        # 获取texttrace信息
        try:
            texttrace = page.get_texttrace()
            print(f"Texttrace spans数量: {len(texttrace)}")
            
            # 分析texttrace结构
            for i, span in enumerate(texttrace[:3]):  # 只看前3个span
                print(f"\nSpan {i}: {type(span)}")
                if isinstance(span, dict):
                    print(f"  Keys: {list(span.keys())}")
                    if 'chars' in span:
                        print(f"  字符数量: {len(span['chars'])}")
                        for j, char in enumerate(span['chars'][:3]):  # 只看前3个字符
                            print(f"    字符 {j}: {char}")
                            if len(char) >= 5:
                                unicode_val, glyph_id, x, y, bbox = char[:5]
                                print(f"      Unicode: {unicode_val} ('{chr(unicode_val) if 32 <= unicode_val <= 126 else '?'}')")
                                print(f"      Glyph ID: {glyph_id}")
                                print(f"      位置: ({x}, {y})")
                                print(f"      边界框: {bbox}")
                
        except Exception as e:
            print(f"获取texttrace失败: {e}")
        
        # 测试字符边界框提取和字形边界框匹配
        char_count = 0
        glyph_match_count = 0

        # 使用rawdict格式获取字符信息
        raw_dict = page.get_text("rawdict")

        for block in raw_dict['blocks']:
            if block['type'] == 0:  # 文本块
                for line in block['lines']:
                    for span in line['spans']:
                        for char in span.get('chars', []):
                            char_count += 1
                            char_c = char['c']
                            char_bbox = char['bbox']

                            if char_count <= 10:  # 只显示前10个字符的信息
                                print(f"\n字符 {char_count}:")
                                print(f"  字符: '{char_c}' (Unicode: {ord(char_c) if char_c else 'N/A'})")
                                print(f"  边界框: {char_bbox}")
                                print(f"  原点: {char.get('origin', 'N/A')}")

                                # 尝试在texttrace中找到匹配的字形信息
                                found_glyph = False
                                for trace_span in texttrace:
                                    if isinstance(trace_span, dict) and 'chars' in trace_span:
                                        for trace_char in trace_span['chars']:
                                            if len(trace_char) >= 4:
                                                trace_unicode = trace_char[0]
                                                trace_glyph_id = trace_char[1]
                                                trace_bbox = trace_char[3] if len(trace_char) > 3 else None

                                                # 检查unicode匹配
                                                if trace_unicode == ord(char_c) if char_c else False:
                                                    # 检查位置是否接近
                                                    if trace_bbox and len(trace_bbox) == 4:
                                                        if (abs(trace_bbox[0] - char_bbox[0]) < 1.0 and
                                                            abs(trace_bbox[1] - char_bbox[1]) < 1.0):
                                                            print(f"  匹配的字形ID: {trace_glyph_id}")
                                                            print(f"  Texttrace边界框: {trace_bbox}")

                                                            # 计算字形边界框（模拟MainFrame中的逻辑）
                                                            x0, y0, x1, y1 = trace_bbox
                                                            char_width = x1 - x0
                                                            char_height = y1 - y0
                                                            margin_x = char_width * 0.1
                                                            margin_y = char_height * 0.05
                                                            glyph_bbox = [x0 + margin_x, y0 + margin_y, x1 - margin_x, y1 - margin_y]
                                                            print(f"  计算的字形边界框: {glyph_bbox}")

                                                            found_glyph = True
                                                            glyph_match_count += 1
                                                            break
                                    if found_glyph:
                                        break

                                if not found_glyph:
                                    print(f"  未找到匹配的字形信息")

        print(f"\n总字符数: {char_count}")
        print(f"找到字形匹配的字符数: {glyph_match_count}")
        print(f"匹配率: {glyph_match_count/char_count*100:.1f}%" if char_count > 0 else "匹配率: 0%")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 使用命令行参数或默认文件
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        # 查找当前目录下的PDF文件
        pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
        if pdf_files:
            pdf_path = pdf_files[0]
            print(f"使用找到的PDF文件: {pdf_path}")
        else:
            print("请提供PDF文件路径作为参数，或在当前目录放置PDF文件")
            sys.exit(1)
    
    success = test_glyph_bbox_extraction(pdf_path)
    if success:
        print("\n✓ 测试完成")
    else:
        print("\n✗ 测试失败")
        sys.exit(1)
