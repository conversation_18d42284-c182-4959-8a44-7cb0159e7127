"""Extract images from PDF.

Both raster images and vector graphics are considered:

* Normal images like jpeg or png could be extracted with method ``page.get_text('rawdict')`` 
  and ``Page.get_images()``. Note the process for png images with alpha channel.
* Vector graphics are actually composed of a group of paths, represented by operators like
  ``re``, ``m``, ``l`` and ``c``. They're detected by finding the contours with ``opencv``.
"""

import logging
import fitz
import re
from ..common.Collection import Collection
from ..common.share import BlockType
from ..common.algorithm import recursive_xy_cut, inner_contours, xy_project_profile


class ImagesExtractor:
    """Extract images from PDF."""

    def __init__(self, page: fitz.Page) -> None:
        """Extract images from PDF page.

        Args:
            page (fitz.Page): pdf page to extract images.
        """
        self._page = page

    def clip_page_to_pixmap(
        self, bbox: fitz.Rect = None, rm_image: bool = False, zoom: float = 3.0, alpha: bool = False
    ):
        """Clip page pixmap according to ``bbox``.

        Args:
            bbox (fitz.Rect, optional): Target area to clip. Defaults to None, i.e. entire page.
                Note that ``bbox`` depends on un-rotated page CS, while clipping page is based on
                the final page.
            rm_image (bool): remove images or not.
            zoom (float, optional): Improve resolution by this rate. Defaults to 3.0.

        Returns:
            fitz.Pixmap: The extracted pixmap.
        """
        # hide text and images
        stream_dict = self._hide_page_text_and_images(
            self._page, rm_text=True, rm_image=rm_image
        )

        if bbox is None:
            clip_bbox = self._page.rect

        # transform to the final bbox when page is rotated
        elif self._page.rotation:
            clip_bbox = bbox * self._page.rotation_matrix

        else:
            clip_bbox = bbox

        clip_bbox = self._page.rect & clip_bbox

        # improve resolution
        # - https://pymupdf.readthedocs.io/en/latest/faq.html#how-to-increase-image-resolution
        # - https://github.com/pymupdf/PyMuPDF/issues/181
        matrix = fitz.Matrix(zoom, zoom)
        pix = self._page.get_pixmap(clip=clip_bbox, matrix=matrix, alpha=alpha)  # type: fitz.Pixmap

        # recovery page if hide text
        doc = self._page.parent
        for xref, stream in stream_dict.items():
            doc.update_stream(xref, stream)

        return pix

    def clip_page_to_dict(
        self,
        bbox: fitz.Rect = None,
        rm_image: bool = False,
        clip_image_res_ratio: float = 3.0,
        alpha: bool = True,
    ):
        """Clip page pixmap (without text) according to ``bbox`` and convert to source image.

        Args:
            bbox (fitz.Rect, optional): Target area to clip. Defaults to None, i.e. entire page.
            rm_image (bool): remove images or not.
            clip_image_res_ratio (float, optional): Resolution ratio of clipped bitmap.
                Defaults to 3.0.

        Returns:
            dict: Image raw dict with optimized resolution.
        """
        # 如果bbox为None，使用页面的rect
        if bbox is None:
            bbox = self._page.rect

        # 使用智能分辨率调整策略（不分块）
        return self._process_with_smart_resolution(bbox, rm_image, clip_image_res_ratio, alpha)

    def _process_with_smart_resolution(self, bbox, rm_image, clip_image_res_ratio, alpha):
        """使用智能分辨率调整策略处理图像"""
        region_width = bbox.width
        region_height = bbox.height
        region_pixels = region_width * region_height

        # PIL安全限制：89,478,485像素，使用85%作为安全余量
        pil_safe_limit = 89478485 * 0.85

        print(f"[SMART_RES] 区域分析: {region_width:.0f}×{region_height:.0f} ({region_pixels:,.0f} 像素)")

        # 计算理论最大安全分辨率
        theoretical_max_ratio = (pil_safe_limit / region_pixels) ** 0.5

        # 智能分辨率策略：根据区域大小动态调整
        if region_pixels > 10000000:  # 超大区域 (>1000万像素)
            # 对于超大区域，优先保证稳定性，适度提升质量
            target_ratio = min(clip_image_res_ratio, max(theoretical_max_ratio, 2.0))
            strategy = "超大区域保守策略"
        elif region_pixels > 5000000:   # 大区域 (500-1000万像素)
            # 对于大区域，平衡质量和稳定性
            target_ratio = min(clip_image_res_ratio, max(theoretical_max_ratio, 2.5))
            strategy = "大区域平衡策略"
        else:  # 中小区域 (<500万像素)
            # 对于中小区域，优先保证质量
            target_ratio = min(clip_image_res_ratio, max(theoretical_max_ratio, 3.0))
            strategy = "中小区域质量优先"

        # 最终安全检查
        final_pixels = region_pixels * (target_ratio ** 2)
        if final_pixels > pil_safe_limit:
            # 如果仍然超限，强制降低到安全范围
            target_ratio = (pil_safe_limit / region_pixels) ** 0.5
            strategy += " + 强制安全限制"

        print(f"[SMART_RES] 策略: {strategy}")
        print(f"[SMART_RES] 分辨率: {clip_image_res_ratio:.1f} → {target_ratio:.2f}")
        print(f"[SMART_RES] 最终像素: {final_pixels:,.0f} / {pil_safe_limit:,.0f}")

        pix = self.clip_page_to_pixmap(
            bbox=bbox, rm_image=rm_image, zoom=target_ratio, alpha=alpha
        )

        return self._to_raw_dict(pix, bbox)

    def extract_images_form_block(self):
        import io
        from PIL import Image
        objects = self._page.get_text("dict")
        
        # 遍历blocks查找图像
        images = []
        text_blocks = []
        for block_index, block in enumerate(objects.get("blocks", [])):
            if block.get("type") == 0:
                text_blocks.append(block)

            if block.get("type") == 1:  # 图像类型
                # 直接从block中获取image数据
                if "image" in block and block["image"]:
                    img_data = block["image"]
                    img_ext = block.get("ext", "png")  # 默认使用png
                    
                    # 确定图像位置关系（是否在文档内容之后）
                    behind_doc = True
                    for text_block in text_blocks:
                        if block["bbox"]:
                            # 计算重叠面积
                            overlap_area = self.calculate_overlap_area(block["bbox"], text_block["bbox"])
                            if overlap_area > 0:
                                behind_doc = block["number"] < text_block["number"]
                                break
                                
                    # 检查是否有mask，如果有，则处理透明通道
                    if "mask" in block and block["mask"]:
                        try:
                            print(f"    正在处理图像的mask透明通道")
                            # 将原始图像数据转为PIL图像
                            img = Image.open(io.BytesIO(img_data))
                            img = img.convert("RGBA") if img.mode != "RGBA" else img
                            
                            # 处理mask数据
                            mask_data = block["mask"]
                            mask = Image.open(io.BytesIO(mask_data)) if isinstance(mask_data, bytes) else None
                            
                            if mask:
                                # 将mask转换为适合的alpha通道
                                if mask.mode == "L":
                                    # 直接用于alpha通道
                                    r, g, b = img.split()[:3]
                                    img = Image.merge("RGBA", (r, g, b, mask))
                                elif mask.mode == "1":
                                    # 二值图像需要转换
                                    mask = mask.convert("L")
                                    r, g, b = img.split()[:3]
                                    img = Image.merge("RGBA", (r, g, b, mask))
                                
                                # 将处理后的图像转换回字节数据
                                output_io = io.BytesIO()
                                img.save(output_io, format="PNG")
                                img_data = output_io.getvalue()
                                img_ext = "png"  # 强制使用PNG保留透明通道
                                print(f"    成功处理mask透明通道")
                        except Exception as e:
                            print(f"处理mask时出错: {e}")
                    else:
                        print(f"    图像没有mask透明通道，直接使用")
                    
                    # 无论是否有mask，都添加图像到结果列表
                    images.append({
                        "type": BlockType.IMAGE.value,
                        "bbox": tuple(block["bbox"]),
                        "width": block["width"],
                        "height": block["height"],
                        "image": img_data,
                        "ext": img_ext,
                        "behind_doc": behind_doc
                    })
        return images    
        
    def extract_images(self, clip_image_res_ratio: float = 3.0, skip_extraction: bool = False):
        """Extract normal images with ``Page.get_images()``.

        Args:
            clip_image_res_ratio (float, optional): Resolution ratio of clipped bitmap.
                Defaults to 3.0.
            skip_extraction (bool, optional): If True, skip extracting original images.
                This should be set to True when using clip_page_to_dict with rm_image=False
                to avoid duplicate images. Defaults to False.

        Returns:
            list: A list of extracted and recovered image raw dict.

        .. note::
            ``Page.get_images()`` contains each image only once, which may less than the
            real count of images in a page.
        """
        # 如果skip_extraction为True，则直接返回空列表，避免图片重复提取
        if skip_extraction:
            print("跳过原始图片提取以避免重复")
            return []
            
        # pdf document
        doc = self._page.parent
        rotation = self._page.rotation

        # The final view might be formed by several images with alpha channel only,
        # as shown in issue-123.
        # It's still inconvenient to extract the original alpha/mask image, as a compromise,
        # extract the equivalent image by clipping the union page region for now.
        # https://github.com/dothinking/pdf2docx/issues/123

        # step 1: collect images: [(bbox, item), ..., ]
        ic = Collection()
        for item in self._page.get_images(full=True):
            item = list(item)
            item[-1] = 0

            # find all occurrences referenced to this image
            rects = self._page.get_image_rects(item)
            unrotated_page_bbox = self._page.cropbox  # note the difference to page.rect
            for bbox in rects:
                # ignore small images
                if bbox.get_area() <= 4:
                    continue

                # ignore images outside page
                if not unrotated_page_bbox.intersects(bbox):
                    continue

                # collect images
                ic.append((bbox, item))

        # step 2: group by intersection
        fun = lambda a, b: a[0].intersects(b[0])
        groups = ic.group(fun)

        # step 3: check each group
        images = []
        for group in groups:
            # clip page with the union bbox of all intersected images
            if len(group) > 1:
                clip_bbox = fitz.Rect()
                for bbox, item in group:
                    clip_bbox |= bbox
                raw_dict = self.clip_page_to_dict(
                    clip_bbox, False, clip_image_res_ratio, alpha=True
                )

            else:
                bbox, item = group[0]

                # Regarding images consist of alpha values only, the turquoise color shown in
                # the PDF is not part of the image, but part of PDF background.
                # So, just to clip page pixmap according to the right bbox
                # https://github.com/pymupdf/PyMuPDF/issues/677

                # It's not safe to identify images with alpha values only,
                # - colorspace is None, for pymupdf <= 1.23.8
                # - colorspace is always Colorspace(CS_RGB), for pymupdf==1.23.9-15 -> issue
                # - colorspace is Colorspace(CS_), for pymupdf >= 1.23.16

                # So, use extracted image info directly.
                # image item: (xref, smask, width, height, bpc, colorspace, ...), e.g.,
                # (19, 0, 331, 369, 1, '', '', 'Im1', 'FlateDecode', 0)
                # (20, 24, 1265, 1303, 8, 'DeviceRGB', '', 'Im2', 'FlateDecode', 0)
                # (21, 0, 331, 369, 1, '', '', 'Im3', 'CCITTFaxDecode', 0)
                # (22, 25, 1265, 1303, 8, 'DeviceGray', '', 'Im4', 'DCTDecode', 0)
                # (23, 0, 1731, 1331, 8, 'DeviceGray', '', 'Im5', 'DCTDecode', 0)
                if item[5] == "":
                    # 对于没有颜色空间信息的图像，不使用alpha通道以避免透明区域显示为白色
                    raw_dict = self.clip_page_to_dict(bbox, False, clip_image_res_ratio, alpha=False)

                # normal images
                else:
                    # recover image, e.g., handle image with mask, or CMYK color space
                    pix = self._recover_pixmap(doc, item)

                    # rotate image with opencv if page is rotated
                    raw_dict = self._to_raw_dict(pix, bbox)
                    if rotation:
                        raw_dict["image"] = self._rotate_image(pix, -rotation)

            images.append(raw_dict)

        block_images = self.extract_images_form_block()
        deduped_images = self.deduplicate_images(images, block_images)
        return deduped_images

    @staticmethod
    def deduplicate_images(images1, images2):
        # 去重两组图像，如果两个图像的边界框重叠面积/大图面积 > 80%，则保留大图像
        if not images2:
            return images1
        
        # 合并两个列表
        all_images = images1.copy()
        all_images.extend(images2)
        
        # 通过索引跟踪要删除的图像
        to_remove = set()
        
        # 比较所有图像对
        for i in range(len(all_images)):
            if i in to_remove:
                continue
                
            for j in range(i+1, len(all_images)):
                if j in to_remove:
                    continue
                    
                bbox1 = all_images[i]['bbox']
                bbox2 = all_images[j]['bbox']
                
                # 计算边界框面积
                area1 = (bbox1[2]-bbox1[0]) * (bbox1[3]-bbox1[1])
                area2 = (bbox2[2]-bbox2[0]) * (bbox2[3]-bbox2[1])
                
                # 计算交集
                x_overlap = max(0, min(bbox1[2], bbox2[2]) - max(bbox1[0], bbox2[0]))
                y_overlap = max(0, min(bbox1[3], bbox2[3]) - max(bbox1[1], bbox2[1]))
                intersection_area = x_overlap * y_overlap
                
                # 计算交集与较大区域的比例
                larger_area = max(area1, area2)
                if larger_area > 0 and intersection_area / larger_area > 0.8:
                    # 移除较小的图像
                    if area1 >= area2:
                        to_remove.add(j)
                    else:
                        to_remove.add(i)
                        break  # 如果当前i被标记删除，不再继续比较
        
        # 过滤掉要删除的图像
        return [img for idx, img in enumerate(all_images) if idx not in to_remove]

    def detect_svg_contours(
        self, min_svg_gap_dx: float, min_svg_gap_dy: float, min_w: float, min_h: float, respect_images: bool = True
    ):
        """Find contour of potential vector graphics.

        Args:
            min_svg_gap_dx (float): Merge svg if the horizontal gap is less than this value.
            min_svg_gap_dy (float): Merge svg if the vertical gap is less than this value.
            min_w (float): Ignore contours if the bbox width is less than this value.
            min_h (float): Ignore contours if the bbox height is less than this value.
            respect_images (bool): If True, original images in PDF will be preserved as a whole
                            and not be split further. Defaults to True.

        Returns:
            list: A list of potential svg region: (external_bbox, inner_bboxes:list).
        """
        import cv2 as cv
        from PIL import Image
        import io 
        # clip page and convert to opencv image
        pixmap = self.clip_page_to_pixmap(rm_image=False, zoom=1.0, alpha=True)
        src = self._pixmap_to_cv_image(pixmap)

        debug = False
        if debug:
            pil_img = Image.frombytes("RGB", [pixmap.width, pixmap.height], pixmap.samples)
            pil_img.show()

        # gray and binary
        import numpy as np
        
        # 检查是否有alpha通道
        has_alpha = len(src.shape) > 2 and src.shape[2] == 4
        
        if has_alpha:
            # 从BGRA获取BGR和A通道
            bgr = src[:, :, :3]
            alpha = src[:, :, 3]
            
            # 创建非透明区域的掩码 (alpha > 10)
            mask = alpha > 10
            
            # 将BGR转换为灰度图
            gray = cv.cvtColor(bgr, cv.COLOR_BGR2GRAY)
            
            # 初始化binary图像为全部透明区域（0）
            binary = np.zeros_like(gray)
            
            # 只对非透明区域进行二值化
            _, thresh = cv.threshold(gray, 253, 255, cv.THRESH_BINARY_INV)
            binary[mask] = thresh[mask]
            
            # 为了debug
            print(f"Alpha 通道检测到的非透明像素数量: {np.sum(mask)}")
        else:
            # 没有alpha通道，常规处理
            gray = cv.cvtColor(src, cv.COLOR_BGR2GRAY)
            _, binary = cv.threshold(gray, 253, 255, cv.THRESH_BINARY_INV)

        # 如果需要尊重原始图像边界，先获取PDF中的图像区域
        original_image_bboxes = []
        if respect_images:
            # 获取原始图像的边界框
            doc = self._page.parent
            unrotated_page_bbox = self._page.cropbox
            
            for item in self._page.get_images(full=True):
                item = list(item)
                item[-1] = 0
                # 找到引用此图像的所有位置
                rects = self._page.get_image_rects(item)
                for bbox in rects:
                    # 忽略太小或页面外的图像
                    if bbox.get_area() <= 4 or not unrotated_page_bbox.intersects(bbox):
                        continue
                    # 收集图像边界框
                    original_image_bboxes.append((bbox.x0, bbox.y0, bbox.x1, bbox.y1))
            
            if original_image_bboxes:
                print(f"检测到 {len(original_image_bboxes)} 个原始图像区域")
        
        # 使用recursive_xy_cut进行区域分割
        external_bboxes = recursive_xy_cut(
            binary, min_dx=min_svg_gap_dx, min_dy=min_svg_gap_dy
        )
        
        # 如果需要尊重原始图像边界，合并那些与原始图像重叠的区域
        if respect_images and original_image_bboxes:
            merged_bboxes = []
            processed_indices = set()
            # 记录哪些边界框是原始图像区域
            is_image_area = []
            
            for img_bbox in original_image_bboxes:
                img_rect = fitz.Rect(img_bbox)
                # 查找与此图像重叠的所有边界框
                overlapping_bboxes = []
                overlapping_indices = []
                
                for i, bbox in enumerate(external_bboxes):
                    if i in processed_indices:
                        continue
                        
                    bbox_rect = fitz.Rect(bbox)
                    # 如果边界框与图像有显著重叠，则包含它
                    overlap_area = bbox_rect.intersect(img_rect).get_area()
                    if overlap_area > 0 and (overlap_area / bbox_rect.get_area() > 0.5 or 
                                             overlap_area / img_rect.get_area() > 0.3):
                        overlapping_bboxes.append(bbox)
                        overlapping_indices.append(i)
                        processed_indices.add(i)
                
                # 如果找到重叠边界框，将它们合并
                if overlapping_bboxes:
                    # 合并边界框（取并集）
                    merged_bbox = overlapping_bboxes[0]
                    for bbox in overlapping_bboxes[1:]:
                        merged_bbox = (
                            min(merged_bbox[0], bbox[0]),
                            min(merged_bbox[1], bbox[1]),
                            max(merged_bbox[2], bbox[2]),
                            max(merged_bbox[3], bbox[3])
                        )
                    merged_bboxes.append(merged_bbox)
                    # 标记为图像区域
                    is_image_area.append(True)
            
            # 添加未处理的边界框
            for i, bbox in enumerate(external_bboxes):
                if i not in processed_indices:
                    merged_bboxes.append(bbox)
                    # 非图像区域
                    is_image_area.append(False)
            
            # 用合并后的边界框替换原始边界框
            if merged_bboxes:
                print(f"将 {len(external_bboxes)} 个区域合并为 {len(merged_bboxes)} 个区域")
                external_bboxes = merged_bboxes

        # 计算内部轮廓 - 对于图像区域，不计算内部轮廓
        grouped_inner_bboxes = []
        
        # 检查是否有图像区域标记
        if respect_images and original_image_bboxes and 'is_image_area' in locals():
            # 为每个区域计算内部轮廓，如果是图像区域则直接返回空列表
            for i, bbox in enumerate(external_bboxes):
                if is_image_area[i]:
                    # 对于图像区域，不计算内部轮廓
                    print(f"区域 {bbox} 是图像区域，跳过内部轮廓计算")
                    grouped_inner_bboxes.append([])
                else:
                    # 对于非图像区域，计算内部轮廓
                    grouped_inner_bboxes.append(inner_contours(binary, bbox, min_w, min_h))
        else:
            # 默认情况，为所有区域计算内部轮廓
            grouped_inner_bboxes = [
                inner_contours(binary, bbox, min_w, min_h) for bbox in external_bboxes
            ]

        # combined external and inner contours
        groups = list(zip(external_bboxes, grouped_inner_bboxes))
        
        # 创建图像区域标记信息
        image_areas = []
        if respect_images and original_image_bboxes and 'is_image_area' in locals():
            image_areas = is_image_area

        # plot detected images for debug
        debug = False
        if debug:
            # plot projection profile for each sub-image
            for i, (x0, y0, x1, y1) in enumerate(external_bboxes):
                arr = xy_project_profile(src[y0:y1, x0:x1, :], binary[y0:y1, x0:x1])
                cv.imshow(f"sub-image-{i}", arr)

            for bbox, inner_bboxes in groups:
                # plot external bbox
                x0, y0, x1, y1 = bbox
                cv.rectangle(src, (x0, y0), (x1, y1), (255, 0, 0), 1)

                # plot inner bbox
                for u0, v0, u1, v1 in inner_bboxes:
                    cv.rectangle(src, (u0, v0), (u1, v1), (0, 0, 255), 1)

            cv.imshow("img", src)
            cv.waitKey(0)

        return groups, image_areas

    @staticmethod
    def _to_raw_dict(image: fitz.Pixmap, bbox: fitz.Rect):
        """Store Pixmap ``image`` to raw dict.

        Args:
            image (fitz.Pixmap): Pixmap to store.
            bbox (fitz.Rect): Boundary box the pixmap.

        Returns:
            dict: Raw dict of the pixmap.
        """
        # 检查是否需要转换色彩空间
        if image.colorspace is None:
            # 尝试创建一个标准RGB图像
            try:
                image = fitz.Pixmap(fitz.csRGB, image)
            except Exception as e:
                logging.warning(f"无法将None colorspace图像转换为RGB: {e}")
                return {
                    "type": BlockType.IMAGE.value,
                    "bbox": tuple(bbox),
                    "width": 0,
                    "height": 0,
                    "image": b'',
                }
        elif image.colorspace.n > 3:  # CMYK或其他超过3个频道的色彩空间
            image = fitz.Pixmap(fitz.csRGB, image)

        try:
            # 智能透明度处理：根据图像特征决定是否保留透明度
            if image.alpha:
                from PIL import Image as PILImage
                from io import BytesIO
                import numpy as np

                try:
                    # 将pixmap转换为PIL图像进行分析
                    img_data = image.tobytes("png")
                    pil_img = PILImage.open(BytesIO(img_data))

                    if pil_img.mode == 'RGBA':
                        # 分析alpha通道
                        alpha_array = np.array(pil_img)[:, :, 3]

                        # 检查边缘区域的透明度
                        h, w = alpha_array.shape
                        edge_size = min(10, h//4, w//4)  # 边缘检查区域大小

                        # 获取四个边缘区域
                        edges = [
                            alpha_array[0:edge_size, :],  # 上边缘
                            alpha_array[-edge_size:, :],  # 下边缘
                            alpha_array[:, 0:edge_size],  # 左边缘
                            alpha_array[:, -edge_size:]   # 右边缘
                        ]

                        # 计算边缘透明度
                        edge_transparency_ratios = []
                        for edge in edges:
                            transparent_pixels = np.sum(edge < 255)
                            total_pixels = edge.size
                            transparency_ratio = transparent_pixels / total_pixels
                            edge_transparency_ratios.append(transparency_ratio)

                        avg_edge_transparency = np.mean(edge_transparency_ratios)

                        # 多因素智能判断：综合考虑多个指标决定是否保留透明度

                        # 计算额外的判断因子
                        fully_transparent = np.sum(alpha_array == 0)
                        total_pixels = alpha_array.size
                        full_transparency_ratio = fully_transparent / total_pixels

                        # 检查是否有大量完全透明区域（矢量图形合成的特征）
                        has_large_transparent_areas = full_transparency_ratio > 0.3

                        # 检查alpha值的分布复杂度
                        unique_alpha = np.unique(alpha_array)
                        has_complex_alpha = len(unique_alpha) > 50

                        # 多因素判断逻辑
                        should_preserve_transparency = (
                            avg_edge_transparency > 0.85 and  # 边缘高透明度
                            has_large_transparent_areas and   # 有大量透明区域
                            has_complex_alpha                 # 复杂的alpha分布
                        )

                        if should_preserve_transparency:
                            logging.info(f"多因素判断保留透明度: 边缘透明度{avg_edge_transparency:.1%}, 完全透明区域{full_transparency_ratio:.1%}, Alpha复杂度{len(unique_alpha)}")

                            # 清理边缘透明度：将边缘的半透明像素转换为完全透明或完全不透明
                            cleaned_alpha = alpha_array.copy()

                            # 对于边缘区域，应用更严格的透明度阈值
                            h, w = alpha_array.shape
                            edge_size = min(10, h//4, w//4)

                            # 处理四个边缘
                            edge_regions = [
                                (0, edge_size, 0, w),      # 上边缘
                                (h-edge_size, h, 0, w),    # 下边缘
                                (0, h, 0, edge_size),      # 左边缘
                                (0, h, w-edge_size, w)     # 右边缘
                            ]

                            for y1, y2, x1, x2 in edge_regions:
                                edge_region = cleaned_alpha[y1:y2, x1:x2]
                                # 将半透明像素转换为完全透明（阈值128）
                                edge_region[edge_region < 128] = 0
                                edge_region[edge_region >= 128] = 255
                                cleaned_alpha[y1:y2, x1:x2] = edge_region

                            # 创建清理后的图像
                            cleaned_img_array = np.array(pil_img)
                            cleaned_img_array[:, :, 3] = cleaned_alpha
                            cleaned_img = PILImage.fromarray(cleaned_img_array, 'RGBA')

                            output_io = BytesIO()
                            cleaned_img.save(output_io, format="PNG")
                            image_data = output_io.getvalue()
                        else:
                            logging.info(f"多因素判断移除透明度: 边缘透明度{avg_edge_transparency:.1%}, 完全透明区域{full_transparency_ratio:.1%}, Alpha复杂度{len(unique_alpha)}")
                            # 预合成白色背景并转换为RGB
                            background = PILImage.new('RGBA', pil_img.size, (255, 255, 255, 255))
                            composite = PILImage.alpha_composite(background, pil_img)
                            rgb_img = composite.convert('RGB')

                            output_io = BytesIO()
                            rgb_img.save(output_io, format="PNG")
                            image_data = output_io.getvalue()
                    else:
                        image_data = img_data

                except Exception as e:
                    logging.warning(f"智能透明度处理失败，使用原始图像: {e}")
                    image_data = image.tobytes()
            else:
                image_data = image.tobytes()

        except Exception as e:
            logging.warning(f"转换图像失败: {e}")
            data = image.samples if hasattr(image, 'samples') and image.samples else b''
            return {
                "type": BlockType.IMAGE.value,
                "bbox": tuple(bbox),
                "width": 0,
                "height": 0,
                "image": data,
            }

        # 如果所有处理都成功，返回正常图像
        return {
            "type": BlockType.IMAGE.value,
            "bbox": tuple(bbox),
            "width": image.width,
            "height": image.height,
            "image": image_data,
        }

    @staticmethod
    def _rotate_image(pixmap: fitz.Pixmap, rotation: int):
        """Rotate image represented by image bytes.

        Args:
            pixmap (fitz.Pixmap): Image to rotate.
            rotation (int): Rotation angle.

        Return: image bytes.
        """
        import cv2 as cv
        import numpy as np

        # convert to opencv image
        img = ImagesExtractor._pixmap_to_cv_image(pixmap)
        h, w = img.shape[:2]  # get image height, width

        # calculate the center of the image
        x0, y0 = w // 2, h // 2

        # default scale value for now -> might be extracted from PDF page property
        scale = 1.0

        # rotation matrix
        matrix = cv.getRotationMatrix2D((x0, y0), rotation, scale)

        # calculate the final dimension
        cos = np.abs(matrix[0, 0])
        sin = np.abs(matrix[0, 1])

        # compute the new bounding dimensions of the image
        W = int((h * sin) + (w * cos))
        H = int((h * cos) + (w * sin))

        # adjust the rotation matrix to take into account translation
        matrix[0, 2] += (W / 2) - x0
        matrix[1, 2] += (H / 2) - y0
        
        # 确定是否有alpha通道
        has_alpha = img.shape[2] == 4 if len(img.shape) > 2 else False
        
        # 处理旋转，保留透明度
        if has_alpha:
            # 分离通道
            b, g, r, a = cv.split(img)
            # 旋转RGB通道
            bgr = cv.merge([b, g, r])
            bgr_rotated = cv.warpAffine(bgr, matrix, (W, H))
            # 旋转Alpha通道
            a_rotated = cv.warpAffine(a, matrix, (W, H))
            # 合并旋转后的RGB和Alpha
            b_rotated, g_rotated, r_rotated = cv.split(bgr_rotated)
            rotated_img = cv.merge([b_rotated, g_rotated, r_rotated, a_rotated])
        else:
            # 无透明通道，直接旋转
            rotated_img = cv.warpAffine(img, matrix, (W, H))

        # 使用PNG格式保存，保留透明度
        encode_param = [int(cv.IMWRITE_PNG_COMPRESSION), 9]
        _, im_png = cv.imencode(".png", rotated_img, encode_param)
        return im_png.tobytes()

    @staticmethod
    def _hide_page_text_and_images(page: fitz.Page, rm_text: bool, rm_image: bool):
        """Hide page text and images."""
        # NOTE: text might exist in both content stream and form object stream
        # - content stream, i.e. direct page content
        # - form object, i.e. contents referenced by this page
        xref_list = [xref for (xref, name, invoker, bbox) in page.get_xobjects()]
        xref_list.extend(page.get_contents())

        # (1) hide text
        # render Tr: set the text rendering mode
        # - 3: neither fill nor stroke the text -> invisible
        # read more:
        # - https://github.com/pymupdf/PyMuPDF/issues/257
        # - https://www.adobe.com/content/dam/acom/en/devnet/pdf/pdfs/pdf_reference_archives/PDFReference.pdf
        def hide_text(stream: bytes) -> tuple[bytes, bool]:
            """Force all text in stream to be hidden by setting `3 Tr` after BT and replacing any existing Tr."""
            has_bt = b"BT" in stream
            has_tr = re.search(rb"\b([0-7])\s+Tr\b", stream) is not None

            if not has_bt:
                return stream, False  # no text

            # 1. Replace any existing Tr commands with 3 Tr
            stream = re.sub(rb"\b([0-7])\s+Tr\b", b"3 Tr", stream)

            # 2. Insert `3 Tr` after every `BT`
            stream = re.sub(rb"\bBT\b", b"BT 3 Tr", stream)

            return stream, True

        # (2) hide image
        # https://github.com/pymupdf/PyMuPDF/issues/338
        def hide_images(stream):
            res = stream
            found = False
            # image names, e.g. [[270, 0, 261, 115, 8, 'DeviceRGB', '', 'Im1', 'DCTDecode']]
            img_names = [item[7] for item in page.get_images(full=True)]
            for k in img_names:
                bk = f"/{k} Do".encode()
                if bk in stream:
                    found = True
                    res = res.replace(bk, b"")
            return res, found

        doc = page.parent  # type: fitz.Document
        source = {}
        for xref in xref_list:
            src = doc.xref_stream(xref)

            # try to hide text
            stream, found_text = hide_text(src) if rm_text else (src, False)

            # try to hide images
            stream, found_images = hide_images(stream) if rm_image else (stream, False)

            if found_text or found_images:
                doc.update_stream(xref, stream)
                source[xref] = src  # save original stream

        return source

    @staticmethod
    def _recover_pixmap(doc: fitz.Document, item: list):
        """Restore pixmap with soft mask considered.

        References:

            * https://pymupdf.readthedocs.io/en/latest/document.html#Document.getPageImageList
            * https://pymupdf.readthedocs.io/en/latest/faq.html#how-to-handle-stencil-masks
            * https://github.com/pymupdf/PyMuPDF/issues/670

        Args:
            doc (fitz.Document): pdf document.
            item (list): image instance of ``page.get_images()``.

        Returns:
            fitz.Pixmap: Recovered pixmap with soft mask considered.
        """
        # data structure of `item`:
        # (xref, smask, width, height, bpc, colorspace, ...)
        x = item[0]  # xref of PDF image
        s = item[1]  # xref of its /SMask

        # base image
        pix = fitz.Pixmap(doc, x)

        # reconstruct the alpha channel with the smask if exists
        if s > 0:
            mask = fitz.Pixmap(doc, s)
            if pix.alpha:
                temp = fitz.Pixmap(pix, 0)  # make temp pixmap w/o the alpha
                pix = None  # release storage
                pix = temp

            # check dimension
            if pix.width == mask.width and pix.height == mask.height:
                pix = fitz.Pixmap(pix, mask)  # now compose final pixmap
            else:
                logging.warning(
                    "Ignore image due to inconsistent size of color and mask pixmaps: %s",
                    item,
                )

        # we may need to adjust something for CMYK pixmaps here ->
        # recreate pixmap in RGB color space if necessary
        # NOTE: pix.colorspace may be None for images with alpha channel values only
        if "CMYK" in item[5].upper():
            pix = fitz.Pixmap(fitz.csRGB, pix)

        return pix

    @staticmethod
    def _pixmap_to_cv_image(pixmap: fitz.Pixmap):
        """Convert fitz Pixmap to opencv image.

        Args:
            pixmap (fitz.Pixmap): PyMuPDF Pixmap.
        """
        import cv2 as cv
        import numpy as np

        # 检查pixmap是否有alpha通道
        has_alpha = pixmap.alpha
        
        # 将pixmap转换为numpy数组
        samples = np.frombuffer(pixmap.samples, dtype=np.uint8)
        samples = samples.reshape(pixmap.height, pixmap.width, pixmap.n)
        
        if has_alpha and samples.shape[2] == 4:  # PyMuPDF输出RGBA格式
            # 转换RGBA到BGRA（OpenCV格式）
            r, g, b, a = cv.split(samples)
            bgra_image = cv.merge([b, g, r, a])
            return bgra_image
        elif samples.shape[2] == 3:  # PyMuPDF输出RGB格式  
            # 转换RGB到BGR（OpenCV格式）
            r, g, b = cv.split(samples)
            bgr_image = cv.merge([b, g, r])
            return bgr_image
        elif samples.shape[2] == 1:  # 灰度图像
            # 灰度图像直接返回，OpenCV可以处理
            return samples
        elif samples.shape[2] == 4 and not has_alpha:  # CMYK格式
            # CMYK需要转换为RGB，然后再转BGR
            # 先转换为RGB的Pixmap，然后处理
            rgb_pix = fitz.Pixmap(fitz.csRGB, pixmap)
            rgb_samples = np.frombuffer(rgb_pix.samples, dtype=np.uint8)
            rgb_samples = rgb_samples.reshape(rgb_pix.height, rgb_pix.width, rgb_pix.n)
            # 转换RGB到BGR
            r, g, b = cv.split(rgb_samples)
            bgr_image = cv.merge([b, g, r])
            rgb_pix = None  # 释放内存
            return bgr_image
        else:
            # 其他未知格式，使用PyMuPDF的tobytes()方法作为后备
            img_byte = pixmap.tobytes()
            return cv.imdecode(np.frombuffer(img_byte, np.uint8), cv.IMREAD_UNCHANGED)

    @staticmethod
    def calculate_overlap_area(bbox1, bbox2):
        """
        计算两个边界框之间的重叠面积
        
        Args:
            bbox1 (tuple): 第一个边界框，格式为(x0, y0, x1, y1)
            bbox2 (tuple): 第二个边界框，格式为(x0, y0, x1, y1)
        
        Returns:
            float: 重叠面积，如果没有重叠则返回0
        """
        # 确保bbox是正确的格式
        if not bbox1 or not bbox2 or len(bbox1) != 4 or len(bbox2) != 4:
            return 0
        
        # 计算重叠区域的坐标
        x_overlap = max(0, min(bbox1[2], bbox2[2]) - max(bbox1[0], bbox2[0]))
        y_overlap = max(0, min(bbox1[3], bbox2[3]) - max(bbox1[1], bbox2[1]))
        
        # 计算重叠面积
        overlap_area = x_overlap * y_overlap
        return overlap_area
