# 字形边界显示功能实现总结

## 🎯 目标

在PDF预览窗口中增加一个显示字形边界的选项，将字符的gbox用橙色绘制出来，可以参考字符边界的处理和绘制。

## 🔧 实现的功能

### 1. 新增字形边界显示选项

在预览窗口的控制面板中添加了"显示字形边界"复选框：

```python
# 显示字形边界复选框
state['show_glyph_bbox_var'] = tk.BooleanVar(value=False)
show_glyph_bbox = tk.Checkbutton(control_frame, text="显示字形边界", 
                               variable=state['show_glyph_bbox_var'], 
                               command=lambda p=pdf_path: self.display_page(int(state['current_page'].get()) - 1, p),
                               font=("Helvetica", 10),
                               bg='#f0f0f0')
show_glyph_bbox.pack(side=tk.LEFT, padx=5, pady=5)
```

### 2. 字形边界框提取方法

实现了`get_glyph_bbox`方法来提取字符的字形边界框：

```python
def get_glyph_bbox(self, char, page_idx, pdf_path):
    """获取字符的字形边界框
    Args:
        char: 字符对象
        page_idx: 页面索引
        pdf_path: PDF文件路径
    Returns:
        字形边界框坐标 [x0, y0, x1, y1] 或 None
    """
```

**核心逻辑：**
- 使用PyMuPDF的`get_texttrace()`方法获取详细的字符信息
- 通过Unicode值和位置匹配找到对应的字形信息
- 从texttrace数据中提取字形ID和边界框
- 计算比字符边界框稍小的字形边界框

### 3. 字形边界框绘制

在`display_page`方法中添加了字形边界框的绘制逻辑：

```python
# 绘制字形边界框
glyph_color = (255, 165, 0)  # 橙色
glyph_width = 1  # 字形边框宽度固定为1像素

if state['show_glyph_bbox_var'].get():
    glyph_bbox = self.get_glyph_bbox(char, page_idx, pdf_path)
    if glyph_bbox:
        gx0, gy0, gx1, gy1 = glyph_bbox
        gx0, gy0, gx1, gy1 = [coord * state['zoom_scale'] for coord in [gx0, gy0, gx1, gy1]]
        draw.rectangle([gx0, gy0, gx1, gy1], outline=glyph_color, width=glyph_width)
```

### 4. 性能优化

- **缓存机制**：实现了texttrace数据的缓存，避免重复获取
- **匹配优化**：放宽了位置匹配条件，提高匹配成功率
- **边距调整**：使用较小的边距（8%水平，3%垂直）使字形边界框更贴近实际字形

## 🎨 视觉效果

- **字符边界框**：蓝色 (0, 0, 255)
- **字形边界框**：橙色 (255, 165, 0)
- **边框宽度**：1像素（固定，不随缩放变化）

## 📊 技术细节

### texttrace数据结构

```python
# 每个字符的数据格式：
[unicode, glyph_id, (x, y), bbox]
# 例如：
(85, 104, (90.024, 753.336), (90.024, 745.056, 97.107, 756.096))
```

### 字形边界框计算

```python
# 基于texttrace边界框计算字形边界框
char_width = x1 - x0
char_height = y1 - y0
margin_x = char_width * 0.08  # 8%的水平边距
margin_y = char_height * 0.03  # 3%的垂直边距

glyph_x0 = x0 + margin_x
glyph_y0 = y0 + margin_y
glyph_x1 = x1 - margin_x
glyph_y1 = y1 - margin_y
```

## 🧪 测试

创建了测试脚本验证功能：
- `test_glyph_bbox.py`：测试字形边界框提取逻辑
- `test_glyph_display.py`：测试GUI显示功能

## 📝 使用方法

1. 启动PDF2DOCX GUI
2. 选择PDF文件并点击"预览"
3. 在预览窗口中勾选"显示字形边界"
4. 可以同时勾选"显示字符边界"进行对比
5. 使用缩放功能查看不同级别的细节

## ✅ 预期效果

- 字形边界框（橙色）比字符边界框（蓝色）更小更紧密
- 字形边界框更准确地反映字符的实际形状
- 有助于调试字符定位和字形渲染问题
- 支持所有缩放级别和页面导航功能
