# -*- coding: utf-8 -*-

'''
A wrapper of PyMuPDF Page as page engine.
'''

import fitz
import logging
from .RawPage import RawPage
from ..image.ImagesExtractor import ImagesExtractor
from ..shape.Paths import Paths
from ..common.constants import FACTOR_A_HALF
from ..common.Element import Element
from ..common.share import (RectType, debug_plot)
from ..common.algorithm import get_area
from PIL import Image
import io
from ..image.Image import Image
from ..image.ImagesExtractor import ImagesExtractor
from ..common.rotation import get_rotation_matrix_angle
import numpy as np
import cv2 as cv
from PIL import Image

class RawPageFitz(RawPage):
    '''A wrapper of ``fitz.Page`` to extract source contents.'''

    def extract_raw_dict(self, **settings):
        raw_dict = {}
        if not self.page_engine: return raw_dict

        # actual page size
        # `self.page_engine` is the `fitz.Page`.
        *_, w, h = self.page_engine.rect # always reflecting page rotation
        raw_dict.update({ 'width' : w, 'height': h })
        self.width, self.height = w, h

        # pre-processing layout elements. e.g. text, images and shapes
        text_blocks = self._preprocess_text(**settings)
        raw_dict['blocks'] = text_blocks

        # 初始化路径对象
        paths = self._init_paths(**settings)
        
        # 在最外层检查是否需要图像合成
        need_composition = self._check_image_composition_needed(paths, **settings)
        
        # 将结果传递给后续处理
        settings['skip_extraction'] = need_composition
        settings['need_composition'] = need_composition
        
        # 提取图像
        image_blocks = self._preprocess_images(**settings)
        
        # 处理形状
        shapes, images = self._preprocess_shapes(**settings)
        # for img in images:
        #     img = Image.open(io.BytesIO(img['image']))
        #     img.show()
        # 保存形状结果
        raw_dict['shapes'] = shapes
        
        # 去重合并所有图像（来自形状处理和直接提取的图像）
        final_images = ImagesExtractor.deduplicate_images(images, image_blocks)
        raw_dict['blocks'].extend(final_images)

        hyperlinks = self._preprocess_hyperlinks()
        raw_dict['shapes'].extend(hyperlinks)        
       
        # 先使用self.page_engine.rotation_matrix 对text_blocks所有数据进行一次旋转
        text_blocks = self._rotate_text_blocks(text_blocks, self.page_engine.rotation_matrix)

        # 计算旋转矩阵
        rotation_matrix, main_wmode = self._calc_page_rotation_matrix(text_blocks)

        return raw_dict, rotation_matrix, main_wmode
    
    def _rotate_text_blocks(self, text_blocks, rotation_matrix):
        '''Rotate text blocks by rotation matrix.
        
        适用于字典结构的text_blocks，旋转所有块、行、span和字符的bbox和坐标。
        '''
        # 提取矩阵分量用于创建纯旋转矩阵
        a, b, c, d, e, f = rotation_matrix
        pure_rotation_matrix = fitz.Matrix(a, b, c, d, 0, 0)  # 保留旋转部分，平移分量设为0
        
        for block in text_blocks:
            if 'bbox' in block:
                if block['type'] == 1 and 'image' in block and 'needRotate' in block:
                    continue
                # 旋转block的bbox
                x0, y0, x1, y1 = block['bbox']
                rect = fitz.Rect(x0, y0, x1, y1) * rotation_matrix
                block['bbox'] = (rect.x0, rect.y0, rect.x1, rect.y1)
                
                if block['type'] == 1 and 'image' in block:
                    # 图像块不需要额外处理，保持原始字节数据
                    img_bytes = block['image']
                    if img_bytes:
                        # 图像数据存在，但当前不需要旋转处理
                        # 图像旋转在其他地方处理（如RawPage.rotation_by_matrix）
                        pass

                # 处理block的lines
                if 'lines' in block:
                    for line in block['lines']:
                        if 'bbox' in line:
                            # 旋转line的bbox
                            x0, y0, x1, y1 = line['bbox']
                            rect = fitz.Rect(x0, y0, x1, y1) * rotation_matrix
                            line['bbox'] = (rect.x0, rect.y0, rect.x1, rect.y1)
                            
                            # 旋转line的文字方向
                            if 'dir' in line and line['dir']:
                                dir_x, dir_y = line['dir']
                                rotated_dir = fitz.Point(dir_x, dir_y) * pure_rotation_matrix
                                line['dir'] = [round(rotated_dir.x, 2), round(rotated_dir.y, 2)]
                            
                            # 处理line中的spans
                            if 'spans' in line:
                                for span in line['spans']:
                                    if 'bbox' in span:
                                        # 旋转span的bbox
                                        x0, y0, x1, y1 = span['bbox']
                                        rect = fitz.Rect(x0, y0, x1, y1) * rotation_matrix
                                        span['bbox'] = (rect.x0, rect.y0, rect.x1, rect.y1)
                                        
                                        # 处理span中的chars
                                        if 'chars' in span:
                                            for char in span['chars']:
                                                if 'bbox' in char:
                                                    # 旋转char的bbox
                                                    x0, y0, x1, y1 = char['bbox']
                                                    rect = fitz.Rect(x0, y0, x1, y1) * rotation_matrix
                                                    char['bbox'] = (rect.x0, rect.y0, rect.x1, rect.y1)
                                                # 旋转char的origin
                                                if 'origin' in char and char['origin']:
                                                    orig_x, orig_y = char['origin']
                                                    rotated_origin = fitz.Point(orig_x, orig_y) * rotation_matrix
                                                    char['origin'] = (rotated_origin.x, rotated_origin.y)
        return text_blocks

    def _preprocess_text(self, **settings):
        '''Extract text blocks and identify hidden text:
            - normal text: shown in pdf page, i.e. belong to text blocks extracted by `page.get_text('rawdict')`.
            - hidden text: might exists in some pdf pages, but not shown directly.
            https://pymupdf.readthedocs.io/en/latest/functions.html#Page.get_texttrace
            https://pymupdf.readthedocs.io/en/latest/textpage.html
        '''
        ocr = settings['ocr']
        if ocr==1: raise SystemExit("OCR feature is planned but not implemented yet.")
        
        # first try to get text with get_text() for diagnosis
        try:
            page_text = self.page_engine.get_text()
        except Exception as e:
            pass
        
        # all text blocks no matter hidden or not
        sort = settings.get('sort')
        raw = self.page_engine.get_text(
                'rawdict',
                flags=0
                    | fitz.TEXT_MEDIABOX_CLIP
                    | fitz.TEXT_CID_FOR_UNKNOWN_UNICODE
                    # | fitz.TEXT_ACCURATE_BBOXES
                    ,
                sort=sort,
                )
        text_blocks = raw.get('blocks', [])
        
        # potential UnicodeDecodeError issue when trying to filter hidden text:
        # https://github.com/dothinking/pdf2docx/issues/144
        # https://github.com/dothinking/pdf2docx/issues/155
        try:
            spans = self.page_engine.get_texttrace()
        except SystemError:
            logging.warning('Ignore hidden text checking due to UnicodeDecodeError in upstream library.')
        
        # 尝试修正text_blocks中字符bbox的偏移问题
        try:
            text_blocks = self._correct_bbox_offset(text_blocks, spans)
        except Exception as e:
            logging.warning(f"[bbox修正] bbox修正失败: {e}")

        if not spans: return text_blocks

        # ignore hidden text if ocr=0, while extract only hidden text if ocr=2
        if ocr==2:
            f = lambda span: span['type']!=3  # find displayed text and ignore it
        else:
            # find hidden text and ignore it, but preserve Type3 fonts
            # Type3 fonts might be incorrectly marked as type==3 but should not be filtered
            def is_hidden_text_to_filter(span):
                if span['type'] != 3:
                    return False
                # Don't filter Type3 fonts even if marked as hidden text
                font_name = span.get('font', '')
                if 'T3' in font_name or 'Type3' in font_name or font_name.startswith('Unnamed-T'):
                    print(f"[DEBUG] Preserving Type3 font span: font={font_name}, type={span['type']}")
                    return False
                return True
            f = is_hidden_text_to_filter
            #f = lambda span: span['type']==3  # find hidden text and ignore it
        filtered_spans = list(filter(f, spans))
        
        def span_area(bbox):
            x0, y0, x1, y1 = bbox
            return (x1-x0) * (y1-y0)

        # filter blocks by checking span intersection: mark the entire block if 
        # any span is matched
        blocks = []
        for block in text_blocks:
            intersected = False
            for line in block['lines']:
                for span in line['spans']:
                    for filter_span in filtered_spans:
                        intersected_area = get_area(span['bbox'], filter_span['bbox'])
                        if span_area(span['bbox']) <= 0:
                            continue
                        if intersected_area / span_area(span['bbox']) >= FACTOR_A_HALF \
                            and span['font']==filter_span['font']:
                            intersected = True
                            break
                    if intersected: break # skip further span check if found
                if intersected: break     # skip further line check

            # keep block if no any intersection with filtered span
            if not intersected: blocks.append(block)

        return blocks
    
    def _correct_bbox_offset(self, text_blocks, spans):
        """修正text_blocks中字符bbox的偏移问题
        
        通过比较text_blocks和spans中字符的bbox来检测偏移向量,
        并修正text_blocks中的bbox坐标
        
        Args:
            text_blocks: 从get_text('rawdict')获取的文本块
            spans: 从get_texttrace()获取的span列表
            
        Returns:
            list: 修正后的文本块列表
        """
        if not text_blocks or not spans:
            return text_blocks
            
        # 过滤出可见文本的spans (type != 3)
        visible_spans = [span for span in spans if span.get('type', 0) != 3]
        if not visible_spans:
            return text_blocks
            
        # 按顺序提取text_blocks中的所有字符及其bbox
        text_chars = []
        for block in text_blocks:
            if block.get('type', -1) != 0:  # 只处理文本块
                continue
                
            for line in block.get('lines', []):
                # 只处理垂直文本（wmode=1）
                if line.get('wmode', 0) != 1:
                    continue
                    
                for span in line.get('spans', []):
                    chars_info = span.get('chars', [])
                    if chars_info:
                        for char_bbox in chars_info:
                            text_chars.append({
                                'bbox': char_bbox['bbox'],
                                'char': char_bbox['c']
                            })
        
        # 按顺序提取spans中的所有字符及其bbox
        span_chars = []
        for span in visible_spans:
            # 只处理垂直文本（wmode=1）
            if span.get('wmode', 0) != 1:
                continue
                
            chars_info = span.get('chars', [])
            if chars_info:
                for char_bbox in chars_info:
                    if isinstance(char_bbox, (list, tuple)) and len(char_bbox) >= 4:
                        span_chars.append({
                            'bbox': char_bbox[3]
                        })
        
        # 计算偏移向量
        offset_vector = self._calculate_offset_vector_by_index(text_chars, span_chars)
        if offset_vector is None:
            return text_blocks

        # 应用偏移修正
        corrected_blocks = self._apply_offset_correction(text_blocks, offset_vector)
        print(f"[bbox修正] 检测到偏移向量: dx={offset_vector[0]:.2f}, dy={offset_vector[1]:.2f}")
        return corrected_blocks
    
    def _calculate_offset_vector_by_index(self, text_chars, span_chars):
        """通过计算联合bbox计算text_blocks相对于spans的偏移向量
        
        Args:
            text_chars: text_blocks中的字符列表(按顺序)
            span_chars: spans中的字符列表(按顺序)
            
        Returns:
            tuple: (dx, dy) 偏移向量, 如果无法确定则返回None
        """
        if len(text_chars) == 0 or len(span_chars) == 0:
            return None
            
        # 计算text_chars的联合bbox
        text_union_bbox = self._calculate_union_bbox([char['bbox'] for char in text_chars])
        if text_union_bbox is None:
            return None
            
        # 计算span_chars的联合bbox
        span_union_bbox = self._calculate_union_bbox([char['bbox'] for char in span_chars])
        if span_union_bbox is None:
            return None
            
        # 计算偏移向量
        dx = span_union_bbox[0] - text_union_bbox[0]
        dy = span_union_bbox[1] - text_union_bbox[1]
        
        # 只有当偏移足够大时才认为有意义（大于0.5像素）
        if abs(dx) > 0.5 or abs(dy) > 0.5:
            return (dx, dy)
        else:
            return None
    
    def _calculate_union_bbox(self, bboxes):
        """计算多个bbox的联合bbox(相或操作)
        
        Args:
            bboxes: bbox列表, 每个bbox为[x0, y0, x1, y1]
            
        Returns:
            list: 联合bbox [x0, y0, x1, y1], 如果输入为空则返回None
        """
        if not bboxes:
            return None
            
        # 初始化为第一个bbox
        min_x0 = bboxes[0][0]
        min_y0 = bboxes[0][1]
        max_x1 = bboxes[0][2]
        max_y1 = bboxes[0][3]
        
        # 遍历所有bbox，找到最小的左上角和最大的右下角
        for bbox in bboxes[1:]:
            min_x0 = min(min_x0, bbox[0])
            min_y0 = min(min_y0, bbox[1])
            max_x1 = max(max_x1, bbox[2])
            max_y1 = max(max_y1, bbox[3])
            
        return [min_x0, min_y0, max_x1, max_y1]
    
    def _apply_offset_correction(self, text_blocks, offset_vector):
        """应用偏移向量修正text_blocks中的bbox坐标
        
        Args:
            text_blocks: 原始文本块列表
            offset_vector: (dx, dy) 偏移向量
            
        Returns:
            list: 修正后的文本块列表
        """
        dx, dy = offset_vector
        
        # 直接修正原始text_blocks中的bbox坐标
        for block in text_blocks:
            if block.get('type', -1) != 0:  # 只处理文本块
                continue
                
            # 检查block中是否有垂直文本行
            has_vertical_text = False
            for line in block.get('lines', []):
                if line.get('wmode', 0) == 1: 
                    has_vertical_text = True
                    break
            
            # 只有包含垂直文本的block才修正bbox
            if not has_vertical_text:
                continue
                
            # 修正block的bbox
            if 'bbox' in block:
                block['bbox'] = (
                    block['bbox'][0] + dx,
                    block['bbox'][1] + dy,
                    block['bbox'][2] + dx,
                    block['bbox'][3] + dy
                )
                
            for line in block.get('lines', []):
                # 只修正垂直文本行（wmode=1）
                if line.get('wmode', 0) != 1:
                    continue
                    
                # 修正line的bbox
                if 'bbox' in line:
                    line['bbox'] = (
                        line['bbox'][0] + dx,
                        line['bbox'][1] + dy,
                        line['bbox'][2] + dx,
                        line['bbox'][3] + dy
                    )
                    
                for span in line.get('spans', []):
                    # 修正span的bbox
                    if 'bbox' in span:
                        span['bbox'] = (
                            span['bbox'][0] + dx,
                            span['bbox'][1] + dy,
                            span['bbox'][2] + dx,
                            span['bbox'][3] + dy
                        )
                        
                    # 修正span的origin（如果存在）
                    if 'origin' in span:
                        span['origin'] = (
                            span['origin'][0] + dx,
                            span['origin'][1] + dy
                        )
                        
                    # 修正chars中每个字符的bbox
                    chars_info = span.get('chars', [])
                    if chars_info:
                        for char_info in chars_info:
                            if 'bbox' in char_info:
                                char_info['bbox'] = (
                                    char_info['bbox'][0] + dx,
                                    char_info['bbox'][1] + dy,
                                    char_info['bbox'][2] + dx,
                                    char_info['bbox'][3] + dy
                                )
                            if 'origin' in char_info:
                                char_info['origin'] = (
                                    char_info['origin'][0] + dx,
                                    char_info['origin'][1] + dy
                                )
        return text_blocks

    def _calc_page_rotation_matrix(self, blocks):
        '''Calculate rotation matrix based on blocks.
        
        Args:
            blocks (list): Text blocks extracted from the page
            
        Returns:
            fitz.Matrix: Calculated rotation matrix based on text direction and writing mode
        '''
        import math
        from collections import defaultdict
        
        # 获取页面尺寸以用于平移计算
        page_width = self.page_engine.rect.width
        page_height = self.page_engine.rect.height
        
        # 如果没有blocks数据，直接返回默认矩阵
        if not blocks:
            return fitz.Matrix(1, 0, 0, 1, 0, 0), 0
        
        # 第一步：根据dir和wmode分组
        groups = defaultdict(list)
        for block in blocks:
            for line in block.get('lines', []):
                if 'dir' in line and 'wmode' in line:
                    # 创建键：(dir_x, dir_y, wmode)
                    # wmode: writing mode
                    key = (round(line['dir'][0], 2), round(line['dir'][1], 2), line['wmode'])
                    # 计算行的总面积
                    bbox = line.get('bbox', [0, 0, 0, 0])
                    area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                    if area > 0:  # 只处理有效面积
                        groups[key].append((line, area))
        
        # 第二步：找出垂直或水平方向的分组
        horizontal_vertical_dirs = [(1,0), (-1,0), (0,1), (0,-1)]
        filtered_groups = {}
        for key, lines in groups.items():
            dir_x, dir_y, wmode = key
            if (dir_x, dir_y) in horizontal_vertical_dirs:
                filtered_groups[key] = lines
        
        # 如果没有找到垂直或水平方向的分组，返回默认矩阵
        if not filtered_groups:
            return fitz.Matrix(1, 0, 0, 1, 0, 0), 0
        
        # 第三步：计算每个分组的总面积
        group_areas = {}
        for key, lines in filtered_groups.items():
            total_area = sum(area for _, area in lines)
            group_areas[key] = total_area
        
        # 找出面积最大的分组
        max_area = max(group_areas.values())
        max_area_groups = [key for key, area in group_areas.items() if area == max_area]
        
        # 第四步和第五步：计算旋转矩阵并找出旋转角度最小的
        min_angle = float('inf')
        best_matrix = fitz.Matrix(1, 0, 0, 1, 0, 0)
        best_wmode = 0
        
        for key in max_area_groups:
            dir_x, dir_y, wmode = key
            
            # 计算当前方向向量与目标方向向量之间的角度
            if wmode == 0:  # 横向书写，目标是 (1,0)
                target_dir = (1, 0)
            else:  # 竖向书写，目标是 (0,1)
                target_dir = (0, 1)
            
            # 计算旋转角度并调整为90度的倍数
            current_angle = math.atan2(dir_y, dir_x)
            target_angle = math.atan2(target_dir[1], target_dir[0])
            rotation_angle = target_angle - current_angle
            
            # 标准化角度到 [-π, π]
            while rotation_angle > math.pi:
                rotation_angle -= 2 * math.pi
            while rotation_angle < -math.pi:
                rotation_angle += 2 * math.pi
            
            # 将角度四舍五入到最近的90度倍数
            # 弧度转度数为: 角度 = 弧度 * 180 / π
            # 90度 = π/2 弧度
            degree_angle = rotation_angle * 180 / math.pi
            nearest_90_degree = round(degree_angle / 90) * 90
            # 再从度数转回弧度
            rotation_angle = nearest_90_degree * math.pi / 180
            
            abs_angle = abs(rotation_angle)
            
            # 如果这个角度比之前找到的更小，更新最小角度和最佳矩阵
            if abs_angle < min_angle:
                min_angle = abs_angle
                best_wmode = wmode
                
                # 如果角度接近0度，不需要旋转
                if abs(nearest_90_degree) < 1 or abs(nearest_90_degree) > 359:  # 差值小于1度视为0度
                    best_matrix = fitz.Matrix(1, 0, 0, 1, 0, 0)
                else:
                    # 计算旋转后的平移量
                    # 创建90度倍数的旋转矩阵，并添加平移部分
                    # [a, b, c, d, e, f] 其中e, f是平移量
                    if nearest_90_degree == 90:  # 90度旋转
                        # 90度旋转后，需要向右平移页面高度
                        # 原点在左上，旋转90度后，原点需要移动到新坐标系的左上
                        best_matrix = fitz.Matrix(0, 1, -1, 0, page_height, 0)
                    elif nearest_90_degree == 180:  # 180度旋转
                        # 180度旋转后，需要向右平移页面宽度，向下平移页面高度
                        best_matrix = fitz.Matrix(-1, 0, 0, -1, page_width, page_height)
                    elif nearest_90_degree == 270 or nearest_90_degree == -90:  # 270度旋转
                        # 270度旋转后，需要向下平移页面宽度
                        best_matrix = fitz.Matrix(0, -1, 1, 0, 0, page_width)
                    else:
                        # 其他情况应该不会发生，但以防万一返回原始旋转矩阵
                        cos_theta = math.cos(rotation_angle)
                        sin_theta = math.sin(rotation_angle)
                        best_matrix = fitz.Matrix(cos_theta, sin_theta, -sin_theta, cos_theta, 0, 0)
        
        return best_matrix, best_wmode

    def _check_image_composition_needed(self, paths, overlap_threshold=0.8, **settings):
        '''
        检测是否需要进行图片合成处理。
        当返回True时，表示需要进行图片合成，同时也意味着应该跳过原始图像提取。
        
        根据以下条件判断：
        1. 是否存在非等向路径（需要矢量图形转位图）
        2. 是否存在重叠图片（需要合并图层）
        
        Args:
            paths: Paths对象，包含页面中的矢量路径信息
            overlap_threshold: 重叠阈值，当两个图片重叠面积/图片面积大于此值时认为需要合成
            **settings: 其他设置参数
            
        Returns:
            bool: 是否需要图片合成/跳过原始图片提取
        '''
        from ..common.Collection import Collection
        from ..image.ImagesExtractor import ImagesExtractor
        import fitz
        
        # 1. 检查是否存在非等向路径
        has_non_iso_paths = not paths.is_iso_oriented and len(paths._instances) > 0
        print(f"[DEBUG] 检查非等向路径: has_non_iso_paths={has_non_iso_paths}")

        # 如果存在非等向路径，直接返回True
        if has_non_iso_paths:
            print(f"[DEBUG] 存在非等向路径，需要合成")
            return True

        # 1.5. 检查矢量图形交集（忽略线条元素）
        vector_graphics_intersect = self._check_vector_graphics_intersection(paths)
        print(f"[DEBUG] 检查矢量图形交集: vector_graphics_intersect={vector_graphics_intersect}")

        # 如果矢量图形存在交集，返回True
        if vector_graphics_intersect:
            print(f"[DEBUG] 矢量图形存在交集，需要合成")
            return True
            
        # 2. 检查图片重叠情况
        # 如果无法获取页面引擎，返回False
        if not self.page_engine:
            return False
            
        # 获取PDF文档
        doc = self.page_engine.parent
        
        # 直接收集所有图像区域到一个列表中
        image_bboxes = []
        unrotated_page_bbox = self.page_engine.cropbox
        
        # 遍历所有图片并收集其边界框
        for item in self.page_engine.get_images(full=True):
            item = list(item)
            item[-1] = 0
            
            # 获取每个图片的所有出现位置
            rects = self.page_engine.get_image_rects(item)
            
            for bbox in rects:
                # 忽略太小或页面外的图片
                if bbox.get_area() <= 4 or not unrotated_page_bbox.intersects(bbox):
                    continue
                    
                # 收集有效的图像边界框
                image_bboxes.append(bbox)
        
        print(f"[DEBUG] 收集到 {len(image_bboxes)} 个有效图像区域")
            
        # 直接进行两两比较检测重叠
        for i in range(len(image_bboxes)):
            for j in range(i+1, len(image_bboxes)):
                bbox1 = image_bboxes[i]
                bbox2 = image_bboxes[j]
                
                # 计算重叠面积
                intersection = bbox1 & bbox2
                overlap_area = intersection.get_area()
                
                # 计算两个图片的面积
                area1 = bbox1.get_area()
                area2 = bbox2.get_area()
                
                # 计算重叠比例（相对于较小的图像）
                smaller_area = min(area1, area2)
                
                if smaller_area > 0 and overlap_area / smaller_area > overlap_threshold:
                    # 存在重叠超过阈值的图像，需要合成
                    print(f"[DEBUG] 检测到图像重叠，重叠比例: {overlap_area / smaller_area:.1%}")
                    return True
        
        # 3. 检查图像密度和分布模式
        # 如果图像数量较多且分布密集，可能需要合成处理
        print(f"[DEBUG] 检查图像密度: {len(image_bboxes)} 个图像")
        if len(image_bboxes) >= 5:  # 图像数量较多
            # 计算图像的总覆盖面积
            total_image_area = sum(bbox.get_area() for bbox in image_bboxes)
            page_area = unrotated_page_bbox.get_area()

            # 如果图像覆盖了页面的大部分区域，可能需要合成
            coverage_ratio = total_image_area / page_area if page_area > 0 else 0
            print(f"[DEBUG] 图像覆盖率: {coverage_ratio:.1%}")
            if coverage_ratio > 0.3:  # 图像覆盖超过30%的页面
                print(f"[DEBUG] 图像覆盖率过高，需要合成")
                return True

        # 4. 检查是否存在重复图像引用（同一图像在多个位置）
        # 这种情况通常表示表格或条纹效果，需要合成处理
        image_refs = {}
        for item in self.page_engine.get_images(full=True):
            xref = item[0]  # 图像的xref引用
            rects = self.page_engine.get_image_rects(item)
            valid_rects = [rect for rect in rects
                          if rect.get_area() > 4 and unrotated_page_bbox.intersects(rect)]

            if len(valid_rects) > 0:
                image_refs[xref] = len(valid_rects)

        # 如果存在被多次引用的图像，可能需要合成
        max_references = max(image_refs.values()) if image_refs else 0
        print(f"[DEBUG] 图像引用情况: {image_refs}")
        print(f"[DEBUG] 最大引用次数: {max_references}")
        if max_references >= 3:  # 同一图像被引用3次以上
            print(f"[DEBUG] 存在重复引用的图像，需要合成")
            return True

        # 如果没有检测到需要合成的情况，返回False
        return False

    def _check_vector_graphics_intersection(self, paths):
        '''
        检查矢量图形之间是否存在交集，忽略线条元素。

        Args:
            paths: Paths对象，包含页面中的矢量路径信息

        Returns:
            bool: 是否存在矢量图形交集
        '''
        if not paths or len(paths._instances) < 2:
            return False

        # 收集非线条的矢量图形边界框
        vector_graphics_bboxes = []

        for path in paths._instances:
            # 检查路径是否只包含线条元素
            if self._is_line_only_path(path):
                continue  # 忽略纯线条路径

            # 收集非线条的矢量图形边界框
            if path.bbox and path.bbox.get_area() > 0:
                vector_graphics_bboxes.append(path.bbox)

        print(f"[DEBUG] 收集到 {len(vector_graphics_bboxes)} 个非线条矢量图形")

        # 如果少于2个矢量图形，无法产生交集
        if len(vector_graphics_bboxes) < 2:
            return False

        # 检查两两之间是否存在交集
        for i in range(len(vector_graphics_bboxes)):
            for j in range(i+1, len(vector_graphics_bboxes)):
                bbox1 = vector_graphics_bboxes[i]
                bbox2 = vector_graphics_bboxes[j]

                # 检查是否有交集
                if bbox1.intersects(bbox2):
                    intersection = bbox1 & bbox2
                    if intersection.get_area() > 0:
                        print(f"[DEBUG] 检测到矢量图形交集: bbox1={bbox1}, bbox2={bbox2}, intersection={intersection}")
                        return True

        return False

    def _is_line_only_path(self, path, line_threshold=3.0):
        '''
        检查路径是否为线条性质的图形（基于几何特征判断）。

        通过检查路径边界框的宽度和高度来判断：
        - 如果宽度或高度小于阈值，则认为是线条
        - 这样可以正确识别各种线条、多边形边框等

        Args:
            path: Path对象
            line_threshold: 线条判断阈值，宽度或高度小于此值认为是线条

        Returns:
            bool: 是否为线条性质的路径
        '''
        if not path or not hasattr(path, 'bbox'):
            return False

        # 获取路径的边界框
        bbox = path.bbox
        if not bbox or bbox.is_empty:
            return False

        # 计算宽度和高度
        width = bbox.width
        height = bbox.height

        # 如果宽度或高度小于阈值，认为是线条
        is_line = width < line_threshold or height < line_threshold

        print(f"[DEBUG] 路径边界框: {bbox}, 宽度: {width:.2f}, 高度: {height:.2f}, 是否为线条: {is_line}")

        return is_line

    def _preprocess_images(self, **settings):
        '''Extract image blocks. Image block extracted by ``page.get_text('rawdict')`` doesn't 
        contain alpha channel data, so it has to get page images by ``page.get_images()`` and 
        then recover them. Note that ``Page.get_images()`` contains each image only once, i.e., 
        ignore duplicated occurrences.
        '''
        # ignore image if ocr-ed pdf: get ocr-ed text only
        if settings['ocr']==2: return []
        
        # 使用传入的skip_extraction参数
        skip_extraction = settings.get('skip_extraction', False)
        
        return ImagesExtractor(self.page_engine).extract_images(
            settings['clip_image_res_ratio'], 
            skip_extraction=skip_extraction
        )


    def _preprocess_shapes(self, **settings):
        '''Identify iso-oriented paths and convert vector graphic paths to pixmap.'''
        paths = self._init_paths(**settings)
        
        # 使用传入的need_composition参数
        need_composition = settings.get('need_composition', False)
        
        # 根据参数决定是否强制进行图片合成
        shapes_and_images = paths.to_shapes_and_images(
            settings['min_svg_gap_dx'], 
            settings['min_svg_gap_dy'], 
            settings['min_svg_w'], 
            settings['min_svg_h'], 
            settings['clip_image_res_ratio'],
            force_composition=need_composition)
        
        return shapes_and_images
    

    @debug_plot('Source Paths')
    def _init_paths(self, **settings):
        '''Initialize Paths based on drawings extracted with PyMuPDF.'''
        raw_paths = self.page_engine.get_cdrawings()
        return Paths(parent=self).restore(raw_paths)


    def _preprocess_hyperlinks(self):
        """Get source hyperlink dicts.

        Returns:
            list: A list of source hyperlink dict.
        """
        hyperlinks = []
        for link in self.page_engine.get_links():
            if link['kind']!=2: continue # consider internet address only
            hyperlinks.append({
                'type': RectType.HYPERLINK.value,
                'bbox': tuple(link['from']),
                'uri' : link['uri']
            })

        return hyperlinks