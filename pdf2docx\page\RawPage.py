'''A wrapper of pdf page engine (e.g. PyMuPDF, pdfminer) to do the following work:

* extract source contents
* clean up blocks/shapes, e.g. elements out of page
* calculate page margin
* parse page structure roughly, i.e. section and column
'''

from abc import (ABC, abstractmethod)
from ast import Tuple

from pdf2docx.image import ImageBlock
from .BasePage import BasePage
from ..layout.Section import Section
from ..layout.Column import Column
from ..shape.Shape import Hyperlink
from ..shape.Shapes import Shapes
from ..layout.Blocks import Blocks
from ..font.Fonts import Fonts
from ..text.TextSpan import TextSpan
from ..text.Line import Line
from ..common.share import debug_plot
from ..common import constants
from ..common.Collection import Collection
import math
from pdf2docx.shape.Shape import Fill
import fitz
from ..image.ImagesExtractor import ImagesExtractor
from ..common.rotation import get_rotation_matrix_angle
from ..image.ImageSpan import ImageSpan
import numpy as np
import cv2 as cv

class RawPage(BasePage, ABC):
    '''A wrapper of page engine.'''

    def __init__(self, page_engine=None):
        ''' Initialize page layout.

        Args:
            page_engine (Object): Source pdf page.
        '''
        BasePage.__init__(self)
        self.page_engine = page_engine
        self.blocks = Blocks(parent=self)
        self.shapes = Shapes(parent=self)
        self.gaps = []
        self.float_footer_blocks = []
        self.main_wmode = 0
        self.rotation_matrix = fitz.Matrix(0.0)


    @abstractmethod
    def extract_raw_dict(self, **settings):
        '''Extract source data with page engine. Return a dict with the following structure:
        ```
            {
                "width" : w,
                "height": h,
                "blocks": [{...}, {...}, ...],
                "shapes" : [{...}, {...}, ...]
            }
        '''


    @property
    def text(self):
        '''All extracted text in this page, with images considered as ``<image>``.
        Should be run after ``restore()`` data.'''
        return '\n'.join([block.text for block in self.blocks])

    @property
    def raw_text(self):
        '''Extracted raw text in current page. Should be run after ``restore()`` data.'''
        return '\n'.join([block.raw_text for block in self.blocks])


    @debug_plot('Source Text Blocks')
    def restore(self, **settings):
        '''Initialize layout extracted with ``PyMuPDF``.'''
        raw_dict, rotation_matrix, main_wmode = self.extract_raw_dict(**settings)
        self.blocks.restore(raw_dict.get('blocks', []))
        self.shapes.restore(raw_dict.get('shapes', []))
        self.main_wmode = main_wmode
        self.rotation_matrix = rotation_matrix
        self.rotation_by_matrix()
        return self.blocks


    @debug_plot('rotation matrix')
    def rotation_by_matrix(self):
        '''Rotate all elements by rotation matrix.'''
        # 1. 旋转blocks中的所有block
        for block in self.blocks:
            if hasattr(block, 'bbox'):
                block.adjust_bbox_by_rotation_matrix(self.rotation_matrix)

                # 如果block有子元素，也需要旋转它们
                if hasattr(block, 'lines'):
                    for line in block.lines:
                        if hasattr(line, 'bbox'):
                            line.adjust_bbox_by_rotation_matrix(self.rotation_matrix)
                            
                            # 旋转line中的文字方向
                            if hasattr(line, 'dir'):
                                # 使用纯旋转矩阵，去掉平移部分
                                a,b,c,d,e,f = self.rotation_matrix
                                pure_rotation_matrix = fitz.Matrix(a,b,c,d,0,0)  # 保留旋转部分，平移分量设为0
                                rotated_dir = fitz.Point(line.dir[0], line.dir[1]) * pure_rotation_matrix
                                line.dir = [round(rotated_dir.x, 2), round(rotated_dir.y, 2)]
                            
                            # 旋转line中的spans
                            if hasattr(line, 'spans'):
                                for span in line.spans:
                                    if hasattr(span, 'bbox'):
                                        span.adjust_bbox_by_rotation_matrix(self.rotation_matrix)
                                        
                                        # 旋转span中的chars
                                        if hasattr(span, 'chars'):
                                            for char in span.chars:
                                                if hasattr(char, 'bbox'):
                                                    char.adjust_bbox_by_rotation_matrix(self.rotation_matrix)
                                                # 旋转char的origin
                                                if hasattr(char, 'origin') and char.origin:
                                                    char.origin = fitz.Point(char.origin) * self.rotation_matrix
        
                                    if isinstance(span, ImageSpan):
                                        # 直接使用Pixmap处理图像旋转，避免颜色通道问题
                                        img_bytes = span.image
                                        if img_bytes:
                                            try:
                                                # 计算旋转角度
                                                angle = get_rotation_matrix_angle(self.rotation_matrix)
                                                # 直接创建Pixmap并旋转，避免cv.imdecode的颜色通道转换
                                                temp_pixmap = fitz.Pixmap(img_bytes)
                                                span.image = ImagesExtractor._rotate_image(temp_pixmap, -angle)
                                                # 释放临时的Pixmap
                                                temp_pixmap = None
                                            except Exception as e:
                                                print(f"[DEBUG] 图像旋转失败: {e}")
                                                # 保持原图像不变
                                                pass
        # 2. 旋转shapes中的所有shape
        for shape in self.shapes:
            if hasattr(shape, 'bbox'):
                shape.adjust_bbox_by_rotation_matrix(self.rotation_matrix)

        # 3. 旋转页面宽高
        page_bbox = fitz.Rect(self.bbox) * self.rotation_matrix
        self.width = page_bbox.width
        self.height = page_bbox.height
        
    def is_horizontal_write_mode_page(self):
        '''Check whether write mode is horizontal.'''
        return self.main_wmode == 0
    
    def is_vertical_write_mode_page(self):
        '''Check whether write mode is vertical.'''
        return self.main_wmode == 1

    @debug_plot('Cleaned Shapes')
    def clean_up(self, **settings):
        '''Clean up raw blocks and shapes, e.g.

        * remove negative or duplicated instances,
        * detect semantic type of shapes
        '''
        # clean up blocks first
        self.blocks.clean_up(
            settings['float_image_ignorable_gap'],
            settings['line_overlap_threshold'])
        # clean up shapes
        self.shapes.clean_up(
            settings['max_border_width'],
            settings['shape_min_dimension'])
        return self.shapes


    def process_font(self, fonts:Fonts):
        '''Update font properties, e.g. font name, font line height ratio, of ``TextSpan``.

        Args:
            fonts (Fonts): Fonts parsed by ``fonttools``.
        '''
        # get all text span
        spans = []
        for line in self.blocks:
            spans.extend([span for span in line.spans if isinstance(span, TextSpan)])

        # check and update font name, line height
        for span in spans:
            font = fonts.get(span.font)
            if not font: 
                continue

            # update font properties with font parsed by fonttools
            span.font = font.name
            if font.line_height:
                span.line_height = font.line_height * span.size


    def calculate_margin(self, **settings):
        """Calculate page margin.

        .. note::
            Ensure this method is run right after cleaning up the layout, so the page margin is
            calculated based on valid layout, and stay constant.
        """
        # Exclude hyperlink from shapes because hyperlink might exist out of page unreasonably,
        # while it should always within page since attached to text.
        shapes = Shapes([shape for shape in self.shapes if not isinstance(shape, Hyperlink)])

        # return default margin if no blocks exist
        if not self.blocks and not shapes: return (constants.ITP, ) * 4

        x0, y0, x1, y1 = self.bbox
        u0, v0, u1, v1 = self.blocks.bbox | self.shapes.bbox

        # margin
        left = max(u0-x0, 0.0)
        right = max(x1-u1-constants.MINOR_DIST, 0.0)
        top = max(v0-y0, 0.0)
        bottom = max(y1-v1, 0.0)

        # reduce calculated top/bottom margin to leave some free space
        if self.main_wmode == 0:
            top *= settings['page_margin_factor_top']
        bottom *= settings['page_margin_factor_bottom']

        # use normal margin if calculated margin is large enough
        return (
            min(constants.ITP, round(left, 1)),
            min(constants.ITP, round(right,1)),
            min(constants.ITP, round(top,1)),
            min(constants.ITP, round(bottom,1)))

    def __get_all_elements(self):
        """
        获取页面上的所有元素，并分隔Fill类型和非Fill类型元素
        
        Returns:
            tuple: (all_elements, fill_elements)
                all_elements: 非Fill类型的所有元素列表
                fill_elements: Fill类型的元素列表
        """
        from pdf2docx.shape.Shape import Fill
        
        # 添加所有元素
        all_elements = []
        all_elements.extend(list(self.blocks._instances))
        
        # 分离Fill元素和其他Shape元素
        fill_elements = []
        for shape in self.shapes._instances:
            if isinstance(shape, Fill):
                fill_elements.append(shape)
            else:
                all_elements.append(shape)
                
        return all_elements, fill_elements
        
    def __analyze_gap_vertical_regions(self, gaps, page_bbox):
        """
        分析空白间隙的垂直区域，创建不重叠的垂直区间
        
        Args:
            gaps: 包含(x1, x2, value, y_ranges)的列表
            page_bbox: 页面边界框 (x0, y0, x1, y1)
            
        Returns:
            list: 垂直区域列表，每个区域是 (y0, y1, active_gaps)
                y0, y1: 区域的垂直范围
                active_gaps: 影响该区域的间隙列表
        """
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        page_y0, page_y1 = page_bbox[idy0], page_bbox[idy1]
        
        # 准备gaps信息，确保每个gap都有垂直范围信息
        processed_gaps = []
        for gap in gaps:
            # 提取gap信息
            if len(gap) < 3:
                continue
                
            gap_x0, gap_x1, gap_value = gap[0], gap[1], gap[2]
            
            # 获取垂直范围
            y_ranges = []
            if len(gap) > 3 and gap[3]:
                y_ranges = gap[3]
            else:
                # 如果没有垂直范围信息，假设影响整个页面
                y_ranges = [(page_y0, page_y1)]
            
            processed_gaps.append((gap_x0, gap_x1, gap_value, y_ranges))
        
        if not processed_gaps:
            return []
            
        # 收集所有垂直分割点
        y_points = set([page_y0, page_y1])
        for _, _, _, y_ranges in processed_gaps:
            for y0, y1 in y_ranges:
                y_points.add(y0)
                y_points.add(y1)
        
        # 排序分割点，创建不重叠的垂直区间
        y_points = sorted(y_points)
        vertical_ranges = []
        for i in range(len(y_points) - 1):
            y0, y1 = y_points[i], y_points[i+1]
            if y0 < y1:  # 确保区间有效
                vertical_ranges.append((y0, y1))
        
        # 对每个垂直区间，统计影响它的gaps
        result_regions = []
        for y0, y1 in vertical_ranges:
            # 找出影响这个区间的所有gaps
            active_gaps = []
            for gap_x0, gap_x1, gap_value, y_ranges in processed_gaps:
                # 检查gap是否影响这个垂直区间
                for gap_y0, gap_y1 in y_ranges:
                    # 如果区间和gap范围有交集
                    if max(y0, gap_y0) < min(y1, gap_y1):
                        # 使用gap的中点来排序
                        gap_midpoint = round((gap_x0 + gap_x1) / 2, 1)
                        # 保存gap信息，包括中点和原始gap数据
                        active_gaps.append((gap_midpoint, (gap_x0, gap_x1, gap_value)))
                        break
            
            # 按gap中点排序
            active_gaps.sort(key=lambda x: x[0])
            # 提取原始gap数据
            active_gaps = [g[1] for g in active_gaps]
            
            # 并将结果添加到垂直区域列表
            result_regions.append((y0, y1, active_gaps))
        
        return result_regions
        
    def __create_sections_in_vertical_region(self, y0, y1, active_gaps, page_bbox):
        """
        在指定的垂直区域中创建一个section，并根据gaps信息定义列结构
        
        Args:
            y0, y1: 垂直区域的范围
            active_gaps: 作用于该区域的gap列表，对gap排序
            page_bbox: 页面边界框
            
        Returns:
            list: 包含一个section的列表，该section包含根据gaps定义的列结构
        """
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        page_x0, page_x1 = page_bbox[idx0], page_bbox[idx1]
        
        # 创建一个包含整个垂直区域的section
        section = {
            'bbox': (page_x0, y0, page_x1, y1) if self.is_horizontal_write_mode_page() else (y0, page_x0, y1, page_x1),
            'columns': [],
            'gaps_info': active_gaps  # 保存gap信息以供参考
        }
        
        # 如果没有有效的gaps，创建单列section
        if not active_gaps:
            section['columns'].append({
                'bbox': (page_x0, y0, page_x1, y1) if self.is_horizontal_write_mode_page() else (y0, page_x0, y1, page_x1),
                'elements': []
            })
            return [section]
        
        # 根据gaps信息定义列边界
        column_boundaries = [page_x0]  # 起始于页面左边界
        
        # 将每个gap的中点作为列边界
        for gap_x0, gap_x1, _ in active_gaps:
            gap_midpoint = (gap_x0 + gap_x1) / 2
            column_boundaries.append(gap_midpoint)
        
        column_boundaries.append(page_x1)  # 结束于页面右边界
        
        # 创建列结构
        for i in range(len(column_boundaries) - 1):
            col_x0, col_x1 = column_boundaries[i], column_boundaries[i+1]
            # 直接创建列，不考虑列宽度
            section['columns'].append({
                'bbox': (col_x0, y0, col_x1, y1) if self.is_horizontal_write_mode_page() else (y0, col_x0, y1, col_x1),
                'elements': []
            })
        
        # 如果没有创建出有效的列，创建一个默认的单列
        if not section['columns']:
            section['columns'].append({
                'bbox': (page_x0, y0, page_x1, y1) if self.is_horizontal_write_mode_page() else (y0, page_x0, y1, page_x1),
                'elements': []
            })
        
        return [section]
    
    def __is_element_in_section(self, element, section_bbox, threshold=0.5):
        """
        判断元素是否属于给定的分区
        
        Args:
            element: 要检查的元素
            section_bbox: 分区的边界框 (x0, y0, x1, y1)
            threshold: 重叠阈值，表示元素需要与分区重叠的比例
            
        Returns:
            bool: 如果元素属于该分区，返回true
        """
        if not hasattr(element, 'bbox'):
            return False
        
        e_x0, e_y0, e_x1, e_y1 = element.bbox
        s_x0, s_y0, s_x1, s_y1 = section_bbox
        
        # 计算元素面积
        element_area = (e_x1 - e_x0) * (e_y1 - e_y0)
        if element_area <= 0:
            return False
        
        # 计算重叠区域
        overlap_x0 = max(e_x0, s_x0)
        overlap_y0 = max(e_y0, s_y0)
        overlap_x1 = min(e_x1, s_x1)
        overlap_y1 = min(e_y1, s_y1)
        
        # 如果没有重叠，返回false
        if overlap_x0 >= overlap_x1 or overlap_y0 >= overlap_y1:
            return False
        
        # 计算重叠面积与元素面积的比例
        overlap_area = (overlap_x1 - overlap_x0) * (overlap_y1 - overlap_y0)
        coverage = overlap_area / element_area
        
        return coverage >= threshold
        
    def __assign_elements_to_sections(self, sections, elements):
        """
        将元素分配到各个分区中
        
        Args:
            sections: 分区列表
            elements: 要分配的元素列表
            
        Returns:
            list: 已分配元素的分区列表
        """
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)

        # 对每个元素，判断它属于哪个分区
        for element in elements:
            assigned = False
            
            # 尝试将元素分配到最合适的分区
            for section in sections:
                section_bbox = section['bbox']
                if self.__is_element_in_section(element, section_bbox):
                    # 创建新的列，如果还没有创建列
                    if not section.get('columns'):
                        # 创建列，包含bbox和elements字段
                        section['columns'] = [{
                            'bbox': section_bbox,  # 使用section的bbox
                            'elements': []
                        }]
                    
                    # 确定元素应该被分配到哪一列
                    element_assigned_to_column = False
                    element_center_x = (element.bbox[idx0] + element.bbox[idx1]) / 2
                    
                    # 遍历所有列，查找最适合的列
                    for i, column in enumerate(section['columns']):
                        column_bbox = column['bbox']
                        # 如果元素的中心点位于列的范围内，就将其分配到该列
                        if column_bbox[idx0] <= element_center_x <= column_bbox[idx1]:
                            section['columns'][i]['elements'].append(element)
                            element_assigned_to_column = True
                            break
                    
                    # 如果没有找到合适的列（可能是因为列的边界定义不完整），
                    # 则根据元素与列的重叠程度分配
                    if not element_assigned_to_column:
                        # 找出与元素重叠最多的列
                        max_overlap = -1
                        best_column_index = 0
                        
                        for i, column in enumerate(section['columns']):
                            column_bbox = column['bbox']
                            # 计算重叠区域
                            overlap_x0 = max(element.bbox[idx0], column_bbox[idx0])
                            overlap_x1 = min(element.bbox[idx1], column_bbox[idx1])
                            
                            if overlap_x1 > overlap_x0:  # 有重叠
                                overlap_width = overlap_x1 - overlap_x0
                                element_width = element.bbox[idx1] - element.bbox[idx0]
                                overlap_ratio = overlap_width / element_width
                                
                                if overlap_ratio > max_overlap:
                                    max_overlap = overlap_ratio
                                    best_column_index = i
                        
                        # 将元素分配到重叠最多的列
                        section['columns'][best_column_index]['elements'].append(element)
                    
                    assigned = True
                    break
            
            # 如果元素没有分配给任何分区，可以选择如何处理
            # 这里选择将其添加到一个非标准分区中
        
        return sections

    def __assign_fill_elements_to_section(self, sections, fill_elements):
        """
        将Fill元素分配到各个分区和列中
        
        Args:
            sections: 分区列表
            fill_elements: Fill元素列表
            
        Returns:
            list: 已分配Fill元素的分区列表
        """
        if not sections or not fill_elements:
            return sections

        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        import fitz
        from pdf2docx.shape.Shape import Fill
        
        # 按垂直位置排序sections（从上到下）
        sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 对每个section处理Fill元素
        for section in sections:
            section_x0, section_x1 = section['bbox'][idx0], section['bbox'][idx1]
            section_y0, section_y1 = section['bbox'][idy0], section['bbox'][idy1]
            
            # 找出与当前section有交集的Fill元素
            overlapping_fills = []
            for fill in fill_elements:
                if not hasattr(fill, 'bbox') or fill.bbox is None:
                    continue
                    
                fill_y0, fill_y1 = fill.bbox[idy0], fill.bbox[idy1]
                
                # 检查Fill元素是否与当前section有交集
                if fill_y0 < section_y1 and fill_y1 > section_y0:
                    overlapping_fills.append(fill)
            
            # 如果没有重叠的Fill元素，跳过此section
            if not overlapping_fills:
                continue
            
            # 处理单列section
            if len(section['columns']) == 1:
                # 处理与section重叠的Fill元素
                for fill in overlapping_fills:

                    fill_x0, fill_x1 = fill.bbox[idx0], fill.bbox[idx1]
                    fill_y0, fill_y1 = fill.bbox[idy0], fill.bbox[idy1]
                    
                    # 创建一个新的Fill元素，限制在当前section的垂直范围内
                    new_fill = Fill()
                    # 复制必要的属性
                    new_fill.color = fill.color if hasattr(fill, 'color') else None
                    new_fill._type = fill._type if hasattr(fill, '_type') else None
                    new_fill._potential_type = fill._potential_type if hasattr(fill, '_potential_type') else None
                    
                    # 调整Fill元素的垂直范围，使其不超出当前section
                    new_bbox = fitz.Rect(
                        fill_x0,
                        max(fill_y0, section_y0),
                        fill_x1,
                        min(fill_y1, section_y1)
                    )
                    new_fill.bbox = new_bbox
                    
                    # 直接添加到单列section中
                    section['columns'][0]['elements'].append(new_fill)
            else:
                # 多列section的处理
                # 为每个列准备一个Fill元素列表
                column_fills = [[] for _ in range(len(section['columns']))]
                
                # 处理与section重叠的Fill元素
                for fill in overlapping_fills:
                    fill_x0, fill_x1 = fill.bbox[idx0], fill.bbox[idx1]
                    fill_y0, fill_y1 = fill.bbox[idy0], fill.bbox[idy1]
                    
                    # 创建一个新的Fill元素，限制在当前section的垂直范围内
                    new_fill = Fill()
                    # 复制必要的属性
                    new_fill.color = fill.color if hasattr(fill, 'color') else None
                    new_fill._type = fill._type if hasattr(fill, '_type') else None
                    new_fill._potential_type = fill._potential_type if hasattr(fill, '_potential_type') else None
                    
                    # 调整Fill元素的范围，使其不超出当前section
                    new_bbox = fitz.Rect(
                        fill_x0,
                        max(fill_y0, section_y0),
                        fill_x1,
                        min(fill_y1, section_y1)
                    ) if self.is_horizontal_write_mode_page() else fitz.Rect(
                        section_y0,
                        max(fill_x0, section_x0),
                        section_y1,
                        min(fill_x1, section_x1)
                    )
                    new_fill.bbox = new_bbox
                    
                    # 检查Fill元素是否跨越多列
                    overlapping_columns = []
                    for j, column in enumerate(section['columns']):
                        col_x0, col_x1 = column['bbox'][idx0], column['bbox'][idx1]
                        # 计算水平方向的重叠
                        overlap_width = min(new_fill.bbox[idx1], col_x1) - max(new_fill.bbox[idx0], col_x0)
                        if overlap_width > 0:
                            overlapping_columns.append((j, col_x0, col_x1, overlap_width))
                    
                    # 如果跨越多列，则分割
                    if len(overlapping_columns) > 1:
                        for j, col_x0, col_x1, _ in overlapping_columns:
                            # 创建新的Fill元素，限制在当前列的水平范围内
                            column_fill = Fill()
                            # 复制必要的属性
                            column_fill.color = new_fill.color
                            column_fill._type = new_fill._type
                            column_fill._potential_type = new_fill._potential_type
                            column_fill.bbox = fitz.Rect(
                                max(new_fill.bbox[idx0], col_x0),
                                new_fill.bbox[idy0],
                                min(new_fill.bbox[idx1], col_x1),
                                new_fill.bbox[idy1]
                            ) if self.is_horizontal_write_mode_page() else fitz.Rect(
                                max(new_fill.bbox[idy0], col_y0),
                                new_fill.bbox[idx0],
                                min(new_fill.bbox[idy1], col_y1),
                                new_fill.bbox[idx1]
                            )
                            column_fills[j].append(column_fill)
                    else:
                        # 如果只在一列内或没有重叠，使用中心点判断
                        fill_center_x = (new_fill.bbox[idx0] + new_fill.bbox[idx1]) / 2
                        
                        # 找出应该分配到的列
                        for j, column in enumerate(section['columns']):
                            col_x0, col_x1 = column['bbox'][idx0], column['bbox'][idx1]
                            if col_x0 <= fill_center_x < col_x1:
                                column_fills[j].append(new_fill)
                                break
                        else:
                            # 如果不在任何列范围内，添加到最后一列
                            if section['columns']:
                                column_fills[-1].append(new_fill)
                
                # 将分割后的Fill元素添加到各个列中
                for j, fills in enumerate(column_fills):
                    for split_fill in fills:
                        section['columns'][j]['elements'].append(split_fill)
        
        return sections

    def __create_fake_sections(self, gaps):
        """
        基于间隙信息创建虚拟分区
        
        Args:
            gaps: 包含(x0, x1, value, y_ranges)的间隙信息列表
            
        Returns:
            list: 创建的分区列表
        """
        # 使用辅助方法获取元素
        all_elements, fill_elements = self.__get_all_elements()
        
        # 获取页面边界
        page_bbox = self.working_bbox
        
        # 分析垂直区域，确保各个区域不重叠
        vertical_regions = self.__analyze_gap_vertical_regions(gaps, page_bbox)
        
        # 分区列表
        sections = []
        
        # 对每个垂直区域创建分区
        if vertical_regions:
            for y0, y1, active_gaps in vertical_regions:
                # 创建区域内的分区
                region_sections = self.__create_sections_in_vertical_region(y0, y1, active_gaps, page_bbox)
                sections.extend(region_sections)
        else:
            # 如果没有垂直区域，创建一个单列section
            sections = self.__create_single_column_section()
        
        # 分配元素到分区
        sections = self.__assign_elements_to_sections(sections, all_elements)
        
        # 分配fill元素到分区
        sections = self.__assign_fill_elements_to_section(sections, fill_elements)
        
        # 合并空分区
        sections = self.__merge_empty_sections(sections)
        
        return sections
                
    	# 将空 section 与周围的非空 section 进行合并，确保没有空 section的存在
    # 空 section指的是没有任何element的section
    def __is_empty_section(self, section):
        """辅助函数：检查section是否为空（没有文本行）"""
        for column in section['columns']:
            for element in column['elements']:
                return False
        return True

    def __is_empty_line_section(self, section):
        """辅助函数：检查section是否为空（没有文本行）"""
        for column in section['columns']:
            for element in column['elements']:
                if isinstance(element, Line):
                    return False
        return True
        
    def __merge_empty_sections(self, sections):
        """
        处理空 section：
        1. 删除空 section（没有文本行的section）
        2. 将非空 section扩展，直到section之间没有空隙
        3. 将空section中的元素合并到相邻的非空section中
        """
        # 如果没有section，直接返回
        if not sections:
            return []
            
        # 如果只有一个section，直接返回
        if len(sections) == 1:
            return sections
        
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        # 按垂直位置排序sections（从上到下）
        sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 步骤1：识别空section和非空section
        non_empty_sections = []
        empty_sections = []
        page_top = float('inf')
        page_bottom = float('-inf')
        page_left = float('inf')
        page_right = float('-inf')
        
        # 收集非空section和空section，并计算页面边界
        for section in sections:
            # 更新页面边界
            page_left = min(page_left, section['bbox'][idx0])
            page_top = min(page_top, section['bbox'][idy0])
            page_right = max(page_right, section['bbox'][idx1])
            page_bottom = max(page_bottom, section['bbox'][idy1])
            
            # 区分空section和非空section
            if not self.__is_empty_line_section(section):
                non_empty_sections.append(section)
            else:
                empty_sections.append(section)
        
        # 如果没有非空section，返回一个空section（整页范围）
        if not non_empty_sections:
            # 如果没有有效的边界，使用默认值
            if page_top == float('inf'):
                page_bbox = (0, 0, 100, 100)
            else:
                page_bbox = (page_left, page_top, page_right, page_bottom) if self.is_horizontal_write_mode_page() \
                    else (page_top, page_left, page_bottom, page_right)
                
            # 创建包含所有元素的新section
            all_elements = []
            for section in sections:
                for column in section['columns']:
                    all_elements.extend(column['elements'])
                    
            return [{
                'bbox': page_bbox,
                'columns': [{
                    'bbox': page_bbox,
                    'elements': all_elements
                }]
            }]
        
        # 步骤2：将空section中的元素合并到最近的非空section中
        for empty_section in empty_sections:
            # 找到最近的非空section
            closest_section = None
            min_distance = float('inf')
            empty_y_center = (empty_section['bbox'][idy0] + empty_section['bbox'][idy1]) / 2
            
            for non_empty_section in non_empty_sections:
                non_empty_y_center = (non_empty_section['bbox'][idy0] + non_empty_section['bbox'][idy1]) / 2
                distance = abs(empty_y_center - non_empty_y_center)
                
                if distance < min_distance:
                    min_distance = distance
                    closest_section = non_empty_section
            
            # 如果找到了最近的非空section，将空section中的元素合并过去
            if closest_section is not None:
                # 扩展非空section的边界框以包含空section
                closest_section['bbox'] = (
                    min(closest_section['bbox'][idx0], empty_section['bbox'][idx0]),
                    min(closest_section['bbox'][idy0], empty_section['bbox'][idy0]),
                    max(closest_section['bbox'][idx1], empty_section['bbox'][idx1]),
                    max(closest_section['bbox'][idy1], empty_section['bbox'][idy1])
                ) if self.is_horizontal_write_mode_page() else (
                    min(closest_section['bbox'][idy0], empty_section['bbox'][idy0]),
                    min(closest_section['bbox'][idx0], empty_section['bbox'][idx0]),
                    max(closest_section['bbox'][idy1], empty_section['bbox'][idy1]),
                    max(closest_section['bbox'][idx1], empty_section['bbox'][idx1])
                )
                
                # 将空section中的所有元素合并到非空section中
                for empty_column in empty_section['columns']:
                    # 找到最合适的目标列（默认使用第一列）
                    target_column = closest_section['columns'][0]
                    min_col_distance = float('inf')
                    empty_col_center = (empty_column['bbox'][idx0] + empty_column['bbox'][idx1]) / 2
                    
                    # 寻找水平距离最近的列
                    for column in closest_section['columns']:
                        col_center = (column['bbox'][idx0] + column['bbox'][idx1]) / 2
                        col_distance = abs(empty_col_center - col_center)
                        
                        if col_distance < min_col_distance:
                            min_col_distance = col_distance
                            target_column = column
                    
                    # 扩展目标列的边界框
                    target_column['bbox'] = (
                        target_column['bbox'][idx0],
                        min(target_column['bbox'][idy0], empty_column['bbox'][idy0]),
                        target_column['bbox'][idx1],
                        max(target_column['bbox'][idy1], empty_column['bbox'][idy1])
                    ) if self.is_horizontal_write_mode_page() else (
                        min(target_column['bbox'][idy0], empty_column['bbox'][idy0]),
                        target_column['bbox'][idx0],
                        max(target_column['bbox'][idy1], empty_column['bbox'][idy1]),
                        target_column['bbox'][idx1]
                    )
                    
                    # 将元素添加到目标列中
                    target_column['elements'].extend(empty_column['elements'])
        
        # 步骤3：扩展非空section以填充空隙
        # 再次按垂直位置排序
        non_empty_sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 处理第一个section，将其顶部扩展到页面顶部
        if len(non_empty_sections) > 0:
            first_section = non_empty_sections[0]
            x0, x1 = first_section['bbox'][idx0], first_section['bbox'][idx1]
            y0, y1 = first_section['bbox'][idy0], first_section['bbox'][idy1]
            first_section['bbox'] = (x0, page_top, x1, y1) if self.is_horizontal_write_mode_page() \
                else (page_top, x0, y1, x1)
            
            # 同时更新列的边界
            for column in first_section['columns']:
                col_x0, col_x1 = column['bbox'][idx0], column['bbox'][idx1]
                col_y0, col_y1 = column['bbox'][idy0], column['bbox'][idy1]
                column['bbox'] = (col_x0, page_top, col_x1, col_y1) if self.is_horizontal_write_mode_page() \
                    else (page_top, col_x0, col_y1, col_x1)
        
        # 处理最后一个section，将其底部扩展到页面底部
        if len(non_empty_sections) > 0:
            last_section = non_empty_sections[-1]
            x0, x1 = last_section['bbox'][idx0], last_section['bbox'][idx1]
            y0, y1 = last_section['bbox'][idy0], last_section['bbox'][idy1]
            last_section['bbox'] = (x0, y0, x1, page_bottom) if self.is_horizontal_write_mode_page() \
                else (y0, x0, page_bottom, x1)
            
            # 同时更新列的边界
            for column in last_section['columns']:
                col_x0, col_x1 = column['bbox'][idx0], column['bbox'][idx1]
                col_y0, col_y1 = column['bbox'][idy0], column['bbox'][idy1]
                column['bbox'] = (col_x0, col_y0, col_x1, page_bottom) if self.is_horizontal_write_mode_page() \
                    else (col_y0, col_x0, page_bottom, col_x1)
        
        # 处理中间的sections，填充相邻两个section之间的空隙
        for i in range(1, len(non_empty_sections)):
            prev_section = non_empty_sections[i-1]
            curr_section = non_empty_sections[i]
            
            # 如果两个section之间有空隙
            if prev_section['bbox'][idy1] < curr_section['bbox'][idy0]:
                # 计算空隙中点
                mid_y = (prev_section['bbox'][idy1] + curr_section['bbox'][idy0]) / 2
                
                # 扩展前一个section的底部
                x0, y0, x1, y1 = prev_section['bbox'][idx0], prev_section['bbox'][idy0], prev_section['bbox'][idx1], prev_section['bbox'][idy1]
                prev_section['bbox'] = (x0, y0, x1, mid_y) if self.is_horizontal_write_mode_page() \
                    else (y0, x0, mid_y, x1)
                
                # 扩展当前section的顶部
                x0, y0, x1, y1 = curr_section['bbox'][idx0], curr_section['bbox'][idy0], curr_section['bbox'][idx1], curr_section['bbox'][idy1]
                curr_section['bbox'] = (x0, mid_y, x1, y1) if self.is_horizontal_write_mode_page() \
                    else (mid_y, x0, y1, x1)
                
                # 同时更新列的边界
                for column in prev_section['columns']:
                    col_x0, col_y0, col_x1, col_y1 = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                    column['bbox'] = (col_x0, col_y0, col_x1, mid_y) if self.is_horizontal_write_mode_page() \
                        else (col_y0, col_x0, mid_y, col_x1)
                
                for column in curr_section['columns']:
                    col_x0, col_y0, col_x1, col_y1 = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                    column['bbox'] = (col_x0, mid_y, col_x1, col_y1) if self.is_horizontal_write_mode_page() \
                        else (mid_y, col_x0, col_y1, col_x1)
            # 如果两个section有重叠
            elif prev_section['bbox'][idy1] > curr_section['bbox'][idy0]:
                # 计算重叠区域的中点
                mid_y = (prev_section['bbox'][idy1] + curr_section['bbox'][idy0]) / 2
                
                # 调整前一个section的底部边界
                x0, y0, x1, y1 = prev_section['bbox'][idx0], prev_section['bbox'][idy0], prev_section['bbox'][idx1], prev_section['bbox'][idy1]
                prev_section['bbox'] = (x0, y0, x1, mid_y) if self.is_horizontal_write_mode_page() \
                    else (y0, x0, mid_y, x1)
                
                # 调整当前section的顶部边界
                x0, y0, x1, y1 = curr_section['bbox'][idx0], curr_section['bbox'][idy0], curr_section['bbox'][idx1], curr_section['bbox'][idy1]
                curr_section['bbox'] = (x0, mid_y, x1, y1) if self.is_horizontal_write_mode_page() \
                    else (mid_y, x0, y1, x1)
                
                # 同时调整列的边界
                for column in prev_section['columns']:
                    col_x0, col_y0, col_x1, col_y1 = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                    column['bbox'] = (col_x0, col_y0, col_x1, mid_y) if self.is_horizontal_write_mode_page() \
                        else (col_y0, col_x0, mid_y, col_x1)
                
                for column in curr_section['columns']:
                    col_x0, col_y0, col_x1, col_y1 = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                    column['bbox'] = (col_x0, mid_y, col_x1, col_y1) if self.is_horizontal_write_mode_page() \
                        else (mid_y, col_x0, col_y1, col_x1)
        
        return non_empty_sections

    def __create_single_column_section(self):
        """
        创建单列布局的section
        
        Returns:
            list: 包含一个单列section的列表
        """
        # 获取页面边界
        page_bbox = self.working_bbox
        x0, y0, x1, y1 = page_bbox
        
        # 创建单列section
        section = {
            'bbox': (x0, y0, x1, y1),
            'columns': [
                {
                    'bbox': (x0, y0, x1, y1),
                    'elements': []
                }
            ]
        }
        
        return [section]

    def __merge_empty_columns(self, gaps, sections):
        """
        合并空列并相应调整gaps信息。
        
        空列定义：不包含任何文字Line的column。
        合并规则：
        1. 优先合并相邻的两个空列
        2. 空列有两个非空列相邻时，向面积较小的列合并
        3. 空列仅有一个非空列相邻时，向该列合并
        
        Args:
            gaps: 分栅gaps信息
            sections: section列表
            
        Returns:
            tuple: (调整后的gaps, 调整后的sections)
        """
        if not sections or not gaps:
            return gaps, sections
            
        # 处理每个section
        modified_gaps = False  # 标记gaps是否需要更新
        
        for section in sections:
            # 检查section是否有多个列
            if 'columns' not in section or len(section['columns']) <= 1:
                continue
                
            # 标记空列
            empty_columns = []
            for i, column in enumerate(section['columns']):
                is_empty = True
                
                # 判断列是否为空（不包含文字Line）
                if 'elements' in column:
                    for element in column['elements']:
                        # 检查元素是否为Line类型并且包含文字
                        if isinstance(element, Line):
                            is_empty = False
                            break
                
                if is_empty:
                    empty_columns.append(i)
            
            # 如果没有空列，继续处理下一个section
            if not empty_columns:
                continue
                
            # 标记已经合并的列
            merged_indices = set()
            new_columns = []
            
            # 第一步：合并相邻的空列
            i = 0
            while i < len(section['columns']):
                if i in merged_indices:
                    i += 1
                    continue
                    
                if i in empty_columns and i+1 < len(section['columns']) and i+1 in empty_columns:
                    # 找到两个相邻的空列，合并它们
                    col1 = section['columns'][i]
                    col2 = section['columns'][i+1]
                    
                    # 计算新的bbox
                    x0 = min(col1['bbox'][0], col2['bbox'][0])
                    y0 = min(col1['bbox'][1], col2['bbox'][1])
                    x1 = max(col1['bbox'][2], col2['bbox'][2])
                    y1 = max(col1['bbox'][3], col2['bbox'][3])
                    
                    merged_col = {
                        'bbox': (x0, y0, x1, y1),
                        'elements': col1.get('elements', []) + col2.get('elements', [])
                    }
                    
                    new_columns.append(merged_col)
                    merged_indices.add(i)
                    merged_indices.add(i+1)
                    i += 2
                    modified_gaps = True
                else:
                    # 如果没有相邻的空列，添加当前列
                    if i not in merged_indices:
                        new_columns.append(section['columns'][i])
                    i += 1
            
            # 更新section的columns
            section['columns'] = new_columns
            
            # 重新计算空列索引
            empty_columns = []
            for i, column in enumerate(section['columns']):
                is_empty = True
                if 'elements' in column:
                    for element in column['elements']:
                        if isinstance(element, Line):
                            is_empty = False
                            break
                if is_empty:
                    empty_columns.append(i)
            
            # 第二步：处理剩下的空列
            if empty_columns:
                merged_indices = set()
                new_columns = []
                
                for i, column in enumerate(section['columns']):
                    if i in merged_indices:
                        continue
                        
                    if i in empty_columns:
                        # 这是一个空列，需要合并
                        left_neighbor = i-1 if i > 0 else None
                        right_neighbor = i+1 if i < len(section['columns'])-1 else None
                        
                        # 检查相邻的非空列
                        valid_neighbors = []
                        if left_neighbor is not None and left_neighbor not in empty_columns and left_neighbor not in merged_indices:
                            left_col = section['columns'][left_neighbor]
                            left_area = (left_col['bbox'][2] - left_col['bbox'][0]) * (left_col['bbox'][3] - left_col['bbox'][1])
                            valid_neighbors.append((left_neighbor, left_area))
                            
                        if right_neighbor is not None and right_neighbor not in empty_columns and right_neighbor not in merged_indices:
                            right_col = section['columns'][right_neighbor]
                            right_area = (right_col['bbox'][2] - right_col['bbox'][0]) * (right_col['bbox'][3] - right_col['bbox'][1])
                            valid_neighbors.append((right_neighbor, right_area))
                        
                        if valid_neighbors:
                            # 根据规则选择合并目标
                            if len(valid_neighbors) == 1:
                                # 只有一个非空相邻列，就选这个
                                target_idx = valid_neighbors[0][0]
                            else:
                                # 有两个非空相邻列，选面积小的
                                valid_neighbors.sort(key=lambda x: x[1])  # 按面积排序
                                target_idx = valid_neighbors[0][0]
                            
                            # 合并空列到目标列
                            empty_col = section['columns'][i]
                            target_col = section['columns'][target_idx]
                            
                            # 计算新的bbox
                            x0 = min(empty_col['bbox'][0], target_col['bbox'][0])
                            y0 = min(empty_col['bbox'][1], target_col['bbox'][1])
                            x1 = max(empty_col['bbox'][2], target_col['bbox'][2])
                            y1 = max(empty_col['bbox'][3], target_col['bbox'][3])
                            
                            target_col['bbox'] = (x0, y0, x1, y1)
                            target_col['elements'] = target_col.get('elements', []) + empty_col.get('elements', [])
                            
                            merged_indices.add(i)
                            modified_gaps = True
                        else:
                            # 没有合适的合并目标，保留该列
                            new_columns.append(column)
                    else:
                        # 非空列，直接保留
                        if i not in merged_indices:
                            new_columns.append(column)
                
                # 更新section的columns
                if merged_indices:
                    section['columns'] = [col for i, col in enumerate(section['columns']) if i not in merged_indices]
        
        # 如果合并了列，调整gaps信息
        # if modified_gaps and gaps:
        #     # 重新计算gaps的有效性
        #     updated_gaps = []
            
        #     for gap in gaps:
        #         x0, x1, width, regions = gap
        #         valid_regions = []
                
        #         for y0, y1 in regions:
        #             # 检查gap是否还在任何section的列边界上
        #             is_valid = False
                    
        #             for section in sections:
        #                 if len(section['columns']) <= 1:
        #                     continue
                            
        #                 for i in range(1, len(section['columns'])):
        #                     left_col = section['columns'][i-1]
        #                     right_col = section['columns'][i]
                            
        #                     # 检查gap是否位于这两列之间
        #                     if (abs(left_col['bbox'][2] - x0) < 1.0 or abs(right_col['bbox'][0] - x0) < 1.0) and \
        #                        y0 <= section['bbox'][3] and y1 >= section['bbox'][1]:
        #                         valid_regions.append((y0, y1))
        #                         is_valid = True
        #                         break
                                
        #                 if is_valid:
        #                     break
                
        #         # 如果这个gap还有有效区域，则保留
        #         if valid_regions:
        #             updated_gaps.append([x0, x1, width, valid_regions])
            
        #     gaps = updated_gaps
                
        return gaps, sections

    def __merge_single_column_sections(self, gaps, sections):
        """
        合并连续出现的单列section。
        
        Args:
            gaps: 分栅gaps信息
            sections: section列表
            
        Returns:
            tuple: (调整后的gaps, 调整后的sections)
        """
        if not sections or len(sections) <= 1:
            return gaps, sections
            
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        # 先按垂直位置排序sections
        sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 标记需要合并的section和合并目标
        merged_sections = []
        merged_indices = set()
        merge_groups = []
        current_group = []
        
        # 先识别出连续的单列section组
        for i, section in enumerate(sections):
            if len(section['columns']) == 1:
                if not current_group:  # 开始新组
                    current_group = [i]
                else:  # 添加到当前组
                    # 检查是否相邻（没有空隙或垂直距离很小）
                    prev_section = sections[current_group[-1]]
                    if abs(prev_section['bbox'][idy1] - section['bbox'][idy0]) < 1.0 or \
                       (prev_section['bbox'][idy1] >= section['bbox'][idy0] and 
                        prev_section['bbox'][idy0] <= section['bbox'][idy1]):
                        current_group.append(i)
                    else:  # 不连续，结束当前组
                        if len(current_group) > 1:  # 只有多于一个section时才需要合并
                            merge_groups.append(current_group.copy())
                        current_group = [i]  # 开始新组
            else:  # 非单列section，结束当前组
                if current_group and len(current_group) > 1:
                    merge_groups.append(current_group.copy())
                current_group = []
        
        # 处理最后一组
        if current_group and len(current_group) > 1:
            merge_groups.append(current_group.copy())
        
        # 如果没有可合并的组，返回原始数据
        if not merge_groups:
            return gaps, sections
        
        # 合并每一组单列section
        for group in merge_groups:
            # 计算新的bbox
            min_x0 = min(sections[i]['bbox'][0] for i in group)
            min_y0 = min(sections[i]['bbox'][1] for i in group)
            max_x1 = max(sections[i]['bbox'][2] for i in group)
            max_y1 = max(sections[i]['bbox'][3] for i in group)
            
            merged_bbox = (min_x0, min_y0, max_x1, max_y1)
            
            # 收集所有元素
            all_elements = []
            for i in group:
                for column in sections[i]['columns']:
                    all_elements.extend(column.get('elements', []))
            
            # 创建新的合并section
            merged_section = {
                'bbox': merged_bbox,
                'columns': [{
                    'bbox': merged_bbox,
                    'elements': all_elements
                }]
            }
            
            merged_sections.append(merged_section)
            merged_indices.update(group)
        
        # 创建新的sections列表，包含未合并的原始section和合并后的section
        new_sections = [sections[i] for i in range(len(sections)) if i not in merged_indices]
        new_sections.extend(merged_sections)
        
        # 重新按垂直位置排序
        new_sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 如果有合并操作，更新gaps信息
        # if merged_indices and gaps:
        #     # 过滤掉失效的gaps区域
        #     for gap in gaps:
        #         x0, x1, width, regions = gap
        #         valid_regions = []
                
        #         for y0, y1 in regions:
        #             # 检查这个区域在新的sections中是否还有效
        #             is_valid = False
        #             for section in new_sections:
        #                 if y0 <= section['bbox'][3] and y1 >= section['bbox'][1] and len(section['columns']) > 1:
        #                     is_valid = True
        #                     break
                    
        #             if is_valid:
        #                 valid_regions.append((y0, y1))
                
        #         gap[3] = valid_regions
            
        #     # 过滤掉没有有效区域的gaps
        #     gaps = [gap for gap in gaps if gap[3]]
        
        return gaps, new_sections

    def __merge_similar_sections(self, gaps, sections):
        gaps, sections = self.__merge_single_column_sections(gaps, sections)
        return gaps, sections

    def __adjust_sections_height(self, sections):
        """
        调整section的高度，确保相邻section之间不存在空隙。
        
        优先级规则：
        1. 列数少的section优先拓高
        2. 如果列数相同，位置处于下方的section优先拓高
        
        Args:
            sections: 要调整的section列表
            
        Returns:
            list: 调整后的section列表
        """
        if not sections:
            return []
        
        # 确保每个section都有必要的字段
        for section in sections:
            if 'columns' not in section:
                section['columns'] = []
                
            for column in section['columns']:
                if 'elements' not in column:
                    column['elements'] = []
        
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)
        
        # 按垂直位置排序sections
        sections.sort(key=lambda s: s['bbox'][idy0])
        
        # 检测并填充相邻section之间的空隙
        for i in range(len(sections)-1):
            current_section = sections[i]
            next_section = sections[i+1]
            
            # 检测当前section的底部和下一个section的顶部之间是否有空隙
            current_bottom = current_section['bbox'][idy1]
            next_top = next_section['bbox'][idy0]
            
            # 如果有空隙，无论有多小，都需要填充
            if next_top > current_bottom:
                # 确定哪个section应该拓高
                # 先检查列数
                current_columns = len(current_section['columns'])
                next_columns = len(next_section['columns'])
                
                # 根据列数和位置决定优先级
                if current_columns < next_columns:
                    # 当前section列数少，拓高当前section
                    self.__extend_section_bottom(current_section, next_top)
                elif current_columns > next_columns:
                    # 下一个section列数少，拓高下一个section
                    self.__extend_section_top(next_section, current_bottom)
                else:
                    # 列数相同，拓高下方的section
                    self.__extend_section_top(next_section, current_bottom)
        
        return sections
    
    def __extend_section_bottom(self, section, new_bottom):
        """
        将section的底部拓展到新的高度。
        
        Args:
            section: 要拓展的section
            new_bottom: 新的底部y坐标
        """
        if not section or 'bbox' not in section:
            return
            
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        # 拓展section的底部
        x0, y0, x1, _ = section['bbox'][idx0], section['bbox'][idy0], section['bbox'][idx1], section['bbox'][idy1]

        section['bbox'] = (x0, y0, x1, new_bottom) if self.is_horizontal_write_mode_page() else (y0, x0, new_bottom, x1)
        
        # 同时拓展section内所有column的底部
        for column in section.get('columns', []):
            if 'bbox' in column:
                col_x0, col_y0, col_x1, _ = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                column['bbox'] = (col_x0, col_y0, col_x1, new_bottom) if self.is_horizontal_write_mode_page() else (col_y0, col_x0, new_bottom, col_x1)
    
    def __extend_section_top(self, section, new_top):
        """
        将section的顶部拓展到新的高度。
        
        Args:
            section: 要拓展的section
            new_top: 新的顶部y坐标
        """
        if not section or 'bbox' not in section:
            return

        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)
            
        # 拓展section的顶部
        x0, _, x1, _ = section['bbox'][idx0], section['bbox'][idy0], section['bbox'][idx1], section['bbox'][idy1]
        section['bbox'] = (x0, new_top, x1, y1) if self.is_horizontal_write_mode_page() else (new_top, x0, y1, x1)
        
        # 同时拓展section内所有column的顶部
        for column in section.get('columns', []):
            if 'bbox' in column:
                col_x0, _, col_x1, col_y1 = column['bbox'][idx0], column['bbox'][idy0], column['bbox'][idx1], column['bbox'][idy1]
                column['bbox'] = (col_x0, new_top, col_x1, col_y1) if self.is_horizontal_write_mode_page() else (new_top, col_x0, col_y1, col_x1)
        
    # 合并稀疏列
    def __merge_sparse_columns(self, sections):
        '''
        合并多列section中相邻的稀疏列。
        
        稀疏列定义：相邻的两列中，当对列中的所有Line按照物理行分组后，
        其中一列的任意row和另外一列的任何row不存在交叉，则认为这两列是稀疏列，可以合并。
        
        Args:
            sections: 分区列表
            
        Returns:
            list: 处理后的分区列表
        '''
        from pdf2docx.common.Collection import Collection
        
        # 如果没有分区，直接返回
        if not sections:
            return sections
        
        def get_to_merge_row(section):
            # 检查每对相邻的列
            to_merge = []
            for i in range(len(section['columns'])-1):
                col1 = section['columns'][i]
                col2 = section['columns'][i+1]
                
                # 跳过没有元素的列
                if not col1.get('elements') or not col2.get('elements'):
                    continue
                
                # 获取两列中的Line元素
                lines1 = [elem for elem in col1['elements'] if hasattr(elem, 'lines')]
                lines2 = [elem for elem in col2['elements'] if hasattr(elem, 'lines')]
                
                # 如果任一列没有Line元素，跳过
                if not lines1 or not lines2:
                    continue
                
                # 从line元素中提取出所有行
                all_lines1 = []
                for line_elem in lines1:
                    all_lines1.extend(line_elem.lines._instances)
                
                all_lines2 = []
                for line_elem in lines2:
                    all_lines2.extend(line_elem.lines._instances)
                
                # 将行转为Collection并分组
                col1_lines = Collection(all_lines1)
                col2_lines = Collection(all_lines2)
                
                # 获取按物理行分组后的行组
                rows1 = col1_lines.group_by_physical_rows()
                rows2 = col2_lines.group_by_physical_rows()
                
                # 检查两列的行是否有重叠
                has_overlap = False
                for row1 in rows1:
                    for row2 in rows2:
                        # 如果有任何一对行是重叠的，标记为有重叠
                        if any(line1.in_same_row(line2) for line1 in row1._instances for line2 in row2._instances):
                            has_overlap = True
                            break
                    if has_overlap:
                        break
                
                # 如果没有重叠，标记这对列为需要合并
                if not has_overlap:
                    to_merge.append((i, i+1))
            return to_merge

        # 遍历所有分区
        for section in sections:
            # 跳过单列或没有列的分区
            if not section.get('columns') or len(section['columns']) < 2:
                continue
                
            # 记录需要合并的列索引对
            while True:
                to_merge = get_to_merge_row(section)
                if not to_merge:
                    break
                columns = self.__merge_columns(section['columns'], to_merge)
                section['columns'] = columns
            
        return sections

    def __merge_columns(self, columns, to_merge):
        '''Merge columns of sections.'''
        # 如果没有要合并的列，直接返回
        if not to_merge:
            return columns
        
        # 复制列对列表，防止改变原始列表
        merge_pairs = list(to_merge)
        # 从后向前处理列对（确保小索引列作为目标）
        merge_pairs.reverse()
        
        # 记录已处理的列对和要删除的列
        processed_pairs = []
        columns_to_remove = set()
        
        # 处理每一对列
        while merge_pairs:
            i, j = merge_pairs.pop(0)  # 取出下一对要处理的列
            
            if i in processed_pairs or j in processed_pairs:
                continue
            
            # 标记该对已处理
            processed_pairs.append(i)
            processed_pairs.append(j)
            
            # 确保 i < j，即小索引列作为目标列
            if i > j:
                i, j = j, i
            
            # 合并两列
            col1 = columns[i]
            col2 = columns[j]
            
            # 扩展目标列的边界框
            col1['bbox'] = (
                min(col1['bbox'][0], col2['bbox'][0]),  # x0
                min(col1['bbox'][1], col2['bbox'][1]),  # y0
                max(col1['bbox'][2], col2['bbox'][2]),  # x1
                max(col1['bbox'][3], col2['bbox'][3])   # y1
            )
            
            # 将第二列的元素合并到第一列
            if 'elements' in col2:
                if 'elements' not in col1:
                    col1['elements'] = []
                col1['elements'].extend(col2['elements'])
            
            # 标记第二列要删除
            columns_to_remove.add(j)
            
        # 最后依次删除所有已标记要删除的列（从大到小删除）
        columns_to_remove = sorted(list(columns_to_remove), reverse=True)
        for idx in columns_to_remove:
            columns.pop(idx)
        
        return columns

    # 合并相同列
    def __merge_same_columns(self, sections):
        '''
        合并多列section中相同的列。
        
        相同列定义：
        对列中所有Line按照Line的众数基线分组后，然后比较两列中的row:
        1. 如果A列中所有row的基线都和B列中的某一个row的基线完全相同，且A列的row在水平方向可以被B列的row完全覆盖，
           或者B列的row可以被A列完全覆盖，则认为这两个row是相同的。
        2. 如果A列中的某个row在B列中找不到与其基线完全相同的row，但A列的该row和B列中的任意row都完全不交叉，
           则也认为该row和B列row完全相同。
        
        如果两相邻的列中所有的row都满足以上两个情况，则认为这两个列是完全相同的，可以合并。
        
        Args:
            sections: 分区列表
            
        Returns:
            list: 处理后的分区列表
        '''
        from pdf2docx.common.Collection import Collection
        import numpy as np
        from collections import defaultdict
        
        # 如果没有分区，直接返回
        if not sections:
            return sections

        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        idy0, idy1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        def is_row_cross(row1, row2):
            '''检查两个row是否交叉。'''
            row1_y0 = min(line.bbox[idy0] for line in row1._instances)
            row1_y1 = max(line.bbox[idy1] for line in row1._instances)
            row2_y0 = min(line.bbox[idy0] for line in row2._instances)
            row2_y1 = max(line.bbox[idy1] for line in row2._instances)
            return row1_y0 < row2_y1 and row1_y1 > row2_y0

        def get_to_merge_row(section):
            to_merge = []
            # 检查每对相邻的列
            for i in range(len(section['columns'])-1):
                col1 = section['columns'][i]
                col2 = section['columns'][i+1]
                
                # 跳过没有元素的列
                if not col1.get('elements') or not col2.get('elements'):
                    continue
                
                # 获取两列中的Line元素
                lines1 = [elem for elem in col1['elements'] if isinstance(elem, Line)]
                lines2 = [elem for elem in col2['elements'] if isinstance(elem, Line)]
                
                # 如果任一列没有Line元素，跳过
                if not lines1 or not lines2:
                    continue
                
                # 将行转为Collection并分组
                col1_lines = Collection(lines1)
                col2_lines = Collection(lines2)
                
                # 按物理行分组
                rows1 = col1_lines.group_by_physical_rows()
                rows2 = col2_lines.group_by_physical_rows()
                
                # 判断两列是否是相同列
                is_same_columns = True
                
                # 第一步：按基线分组
                # 为两列的行创建基线分组字典
                baseline_groups1 = defaultdict(list)
                baseline_groups2 = defaultdict(list)
                
                # 为第一列的每个行组计算基线众数并分组
                for row in rows1:
                    # 收集该行组中所有行的基线值
                    baselines = [round(line.get_baseline_raw_width_page(self.main_wmode), 2) for line in row._instances]

                    # 去除 baseline 中的 inf
                    baselines = [b for b in baselines if b != float('inf')]

                    # 如果没有基线值，跳过该行组
                    if not baselines:
                        continue
                    # 计算最常见的基线值（众数）
                    baseline_mode = max(set(baselines), key=baselines.count)
                    # 将该行组添加到对应基线值的组中
                    baseline_groups1[baseline_mode].append(row)
                
                # 为第二列的每个行组计算基线众数并分组
                for row in rows2:
                    baselines = [round(line.get_baseline_raw_width_page(self.main_wmode), 2) for line in row._instances]
                    # 去除 baseline 中的 inf
                    baselines = [b for b in baselines if b != float('inf')]
                    if not baselines:
                        continue
                    baseline_mode = max(set(baselines), key=baselines.count)
                    baseline_groups2[baseline_mode].append(row)
                
                # 打印分组结果,要求打印出详细的行的文字信息
                def print_rows(rows):
                    for row in rows:
                        for line in row:
                            print(f"{line.raw_text}")

                # print("Base line groups 1:")
                # for baseline, rows in baseline_groups1.items():
                #     print(f"Base line {baseline}: ")
                #     print_rows(rows)

                # print("Base line groups 2:")
                # for baseline, rows in baseline_groups2.items():
                #     print(f"Base line {baseline}: ")
                #     print_rows(rows)

                # 第二步：检查第一列的每个行组是否在第二列中有对应
                for baseline1, baseline1_rows in baseline_groups1.items():
                    # 标记该基线的行是否找到匹配
                    matched = False
                    
                    # 检查第二列中是否有相同基线的行组
                    if baseline1 in baseline_groups2:
                        baseline2_rows = baseline_groups2[baseline1]
                        # 检查垂直覆盖关系
                        for row1 in baseline1_rows:
                            row1_covered = False
                            for row2 in baseline2_rows:
                                # 检查行1是否被行2垂直覆盖，或行2是否被行1垂直覆盖
                                row1_y0 = min(line.bbox[idy0] for line in row1._instances)
                                row1_y1 = max(line.bbox[idy1] for line in row1._instances)
                                row2_y0 = min(line.bbox[idy0] for line in row2._instances)
                                row2_y1 = max(line.bbox[idy1] for line in row2._instances)
                                
                                # 判断垂直覆盖关系
                                if (row1_y0 >= row2_y0 and row1_y1 <= row2_y1) or \
                                   (row2_y0 >= row1_y0 and row2_y1 <= row1_y1):
                                    row1_covered = True
                                    break
                            
                            if not row1_covered:
                                # 行1没有被任何行2覆盖，检查是否与所有行2都不交叉
                                no_overlap = True
                                for row2 in baseline2_rows:
                                    # 直接标记row1和row2是否交叉
                                    if is_row_cross(row1, row2):
                                        no_overlap = False
                                        break
                                
                                if not no_overlap:
                                    # 如果有交叉，则不是相同列
                                    is_same_columns = False
                                    break
                        
                        if is_same_columns:
                            matched = True
                    
                    # 如果该基线的行没有找到匹配
                    if not matched:
                        # 检查这些行是否与另一列的所有行都不交叉
                        no_overlap_with_all = True
                        for row1 in baseline1_rows:
                            for row2 in rows2:
                                if is_row_cross(row1, row2):
                                    no_overlap_with_all = False
                                    break
                            if not no_overlap_with_all:
                                break
                        
                        if not no_overlap_with_all:
                            # 如果有交叉，则不是相同列
                            is_same_columns = False
                            break
                
                # 如果两列被认为是相同的，标记为需要合并
                if is_same_columns:
                    to_merge.append((i, i+1))

            return to_merge

        # 遍历所有分区
        for section in sections:
            # 跳过单列或没有列的分区
            if not section.get('columns') or len(section['columns']) < 2:
                continue

            while True:
                # 记录需要合并的列索引对
                to_merge = get_to_merge_row(section)
                if not to_merge:
                    break
                columns = self.__merge_columns(section['columns'], to_merge)
                section['columns'] = columns
        
        return sections

    def __merge_small_columns(self, sections):
        '''
        合并多列section中的小列（行数少于1行的列）。
        
        小列定义：对列中的所有Line按照物理行分组(group_by_physical_rows)后，
        如果这两列中任意列的行数少于一行，则认为这两列可以合并。
        
        Args:
            sections: 分区列表
            
        Returns:
            list: 处理后的分区列表
        '''
        from pdf2docx.common.Collection import Collection
        from pdf2docx.text.Line import Line
        
        # 如果没有分区，直接返回
        if not sections:
            return sections
            
        def get_to_merge_row(section):
            # 检查每对相邻的列
            to_merge = []
            for i in range(len(section['columns'])-1):
                col1 = section['columns'][i]
                col2 = section['columns'][i+1]
                
                # 跳过没有元素的列
                if not col1.get('elements') or not col2.get('elements'):
                    continue
                
                # 获取两列中的Line元素
                lines1 = [elem for elem in col1['elements'] if isinstance(elem, Line)]
                lines2 = [elem for elem in col2['elements'] if isinstance(elem, Line)]
                
                # 将行转为Collection并分组
                col1_lines = Collection(lines1)
                col2_lines = Collection(lines2)
                
                # 按物理行分组
                rows1 = col1_lines.group_by_rows(factor=0.9)
                rows2 = col2_lines.group_by_rows(factor=0.9)
                
                # 检查两列中是否有任一列的行数少于1行
                if len(rows1) <= 1 or len(rows2) <= 1:
                    # 如果是小列，标记为需要合并
                    to_merge.append((i, i+1))
            return to_merge

        # 遍历所有分区
        for section in sections:
            # 跳过单列或没有列的分区
            if not section.get('columns') or len(section['columns']) < 2:
                continue
            
            while True:
                # 记录需要合并的列索引对
                to_merge = get_to_merge_row(section)
                if not to_merge:
                    break
                columns = self.__merge_columns(section['columns'], to_merge)
                section['columns'] = columns
        
        return sections

    def __merge_special_section_columns(self, gaps, sections):
        '''Merge special sections and their corresponding gaps.'''
        sections = self.__merge_same_columns(sections)
        sections = self.__merge_sparse_columns(sections)
        sections = self.__merge_small_columns(sections)
        return gaps, sections

    def __remove_fake_sections(self, gaps, sections):
        '''Remove fake sections and their corresponding gaps.'''
        gaps, sections = self.__merge_empty_columns(gaps, sections)
        gaps, sections = self.__merge_special_section_columns(gaps, sections)
        gaps, sections = self.__merge_similar_sections(gaps, sections)
        return gaps, sections

    def __adjust_sections_and_gaps(self, sections, gaps):
        """
        调整sections和gaps，包括移除假sections和调整sections高度。
        
        Args:
            sections: section列表
            gaps: gap列表
            
        Returns:
            tuple: (gaps, sections)的元组
        """
        # 移除假sections
        gaps, sections = self.__remove_fake_sections(gaps, sections)
        
        # 调整sections高度
        if sections:  # 确保sections不为空
            sections = self.__adjust_sections_height(sections)
            
        return gaps, sections
        
    def __create_real_sections(self, sections):
        """
        将假的sections转换为真实的Section对象。
        
        Args:
            sections: 假的sections列表，每个section是一个字典
            
        Returns:
            list: 创建的Section对象列表
        """
        if not sections:
            return []

        idx0,idx1 = (0,2) if self.is_horizontal_write_mode_page() else (1,3)
        idy0,idy1 = (1,3) if self.is_horizontal_write_mode_page() else (0,2)

        real_sections = []
        y_ref = self.working_bbox[idy0]
        
        for section_dict in sections:
            # 获取section的边界框和列信息
            section_bbox = section_dict['bbox']
            columns_info = section_dict['columns']
            
            # 创建Section对象
            section = Section()
            
            # 计算section的垂直距离（与上一个section的间距）
            section_y0 = section_bbox[idy0]
            # 对第一个section特殊处理，减去页面边距的影响
            if real_sections:
                before_space = section_y0 - y_ref
            else:
                # 第一个section的before_space应该更小或为0
                before_space = max(0, section_y0 - y_ref)
            section.before_space = round(before_space, 1)
            
            # 更新垂直参考点为当前section的底部
            y_ref = section_bbox[idy1]
            
            # 如果有多个列，计算列间距
            if len(columns_info) > 1:
                # 计算相邻列之间的平均间距
                total_space = 0
                for i in range(len(columns_info) - 1):
                    col1_bbox = columns_info[i]['bbox']
                    col2_bbox = columns_info[i + 1]['bbox']
                    total_space += col2_bbox[idx0] - col1_bbox[idx1]  # 右列左边界 - 左列右边界
                
                avg_space = total_space / (len(columns_info) - 1)
                section.space = round(avg_space, 1)
            else:
                section.space = 0
            
            # 创建并添加每一列
            for column_info in columns_info:
                column_bbox = column_info['bbox']
                column_elements = Collection(column_info['elements'])
                
                # 创建列对象
                column = Column()
                column.update_bbox(column_bbox)  # 设置列的边界框
                
                # 添加元素到列中
                if column_elements:
                    column.add_elements(column_elements)
                
                # 将列添加到区域中
                section.append(column)
            
            # 只添加非空的区域
            if len(section) > 0:
                real_sections.append(section)
        
        return real_sections

    def __create_sections_ex(self, gaps):
        '''
        根据空白间隙的结果创建Section。
        
        Args:
            gaps: 包含(x1, x2, value)的列表，表示空白间隙的起始和结束坐标以及覆盖权重
            
        Returns:
            list: 创建的Section列表
        '''
        fake_sections = self.__create_fake_sections(gaps)
        self.gaps, fake_sections = self.__adjust_sections_and_gaps(fake_sections, gaps)
        return self.__create_real_sections(fake_sections)

    def __merge_results(self, results, new_result, weight, yi_0, yj_1):
        '''
        合并空白间隙的结果。
        
        Args:
            results: 已有的结果列表，每项包含(x1, x2, value, y_ranges),表示空白间隙的起始和结束坐标、覆盖权重以及垂直范围列表
            new_result: 新的空白间隙结果列表，每项为(x1, x2)
            weight: 新间隙的权重
            yi_0: 新间隙的垂直范围的起始坐标（顶部）
            yj_1: 新间隙的垂直范围的结束坐标（底部）
            
        Returns:
            更新后的results列表
        '''
        if not new_result:
            return results

        # 创建新的结果列表（如果为空）
        if results is None or len(results) == 0:
            results = []
        
        # 初始化临时结果列表，用于存储重叠的间隙
        temp_results = []
        # 第一步：处理每个新的空白间隙
        for gap in new_result:
            x1, x2 = gap[0], gap[1]
            # 保留一位小数
            x1 = round(x1, 1)
            x2 = round(x2, 1)
            
            # 检查是否已存在相同的间隙
            found = False
            for idx, item in enumerate(results):
                # 确保结果列表中的项目有垂直范围列表
                if len(item) == 3:
                    item.append([])
                
                if item[0] == x1 and item[1] == x2:
                    found = True
                    # 将新的垂直范围添加到列表中
                    item[3].append((yi_0, yj_1))
                    break
                    
            # 如果不存在，创建新项
            if not found:
                results.append([x1, x2, weight, [(yi_0, yj_1)]])
            else:
                # 如果已存在，添加到临时结果列表
                results[idx][3].append((yi_0, yj_1))
                results[idx][2] += weight


        # 第二步：对每个间隙的垂直范围进行合并
        for idx, item in enumerate(results):
            if len(item) < 4 or not item[3]: 
                continue
            
            # 对范围进行排序（按起始位置）
            ranges = sorted(item[3], key=lambda x: x[0])
            
            # 合并重叠或相邻的范围
            merged_ranges = []
            if ranges:
                current_range = ranges[0]
                
                for next_range in ranges[1:]:
                    # 如果当前范围与下一个范围相邻或重叠
                    if current_range[1] >= next_range[0]:
                        # 合并范围
                        current_range = (current_range[0], max(current_range[1], next_range[1]))
                    else:
                        # 如果不相邻，保存当前范围并开始新的范围
                        merged_ranges.append(current_range)
                        current_range = next_range
                
                # 添加最后一个范围
                merged_ranges.append(current_range)
            else:
                merged_ranges = ranges
            
            # 更新合并后的范围列表
            item[3] = merged_ranges
        
        return results

    def __calculate_space_by_special_rows(self, rows):
        '''
            找到所有行的垂直投影与顶部或底部的交集.

            Args:
                rows (list): A list of rows.
                index (int): The index of the first row.

            Returns:
                list: A list of (空白投影的起始X值, 空白投影的终止X值).
        '''
        if not rows:
            return None
            
        # 获取页面的水平范围
        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        X0, X1 = self.working_bbox[idx0], self.working_bbox[idx1]

        # 存储所有行的列分组结果
        all_columns = []
        
        # 计算平均字符宽度
        avg_char_width = 0
        char_count = 0
        
        def get_char_width_info(element):
            '''Get character width info.'''
            if hasattr(element, 'spans'):
                for span in element.spans:
                    if hasattr(span, 'text'):
                        text = span.text.strip()
                        if text:
                            text_width = span.bbox[idx1] - span.bbox[idx0]
                            text_len = len(text)
                            return text_width, text_len
            return 0, 0

        # 对每一行进行列分组
        for row in rows:
            # 如果行中没有元素，跳过
            if not row:
                continue
                
            # 直接从行中提取元素的水平范围
            element_spans = []
            for element in row:
                # 提取元素的水平范围
                x0, x1 = element.bbox[idx0], element.bbox[idx1]
                element_spans.append((x0, x1))
                
                text_width, text_len = get_char_width_info(element)
                avg_char_width += text_width
                char_count += text_len
            
            # 合并重叠的区间
            if element_spans:
                # 按X坐标排序
                element_spans.sort()
                
                # 合并当前行内重叠的区间
                merged_spans = [element_spans[0]]
                for current in element_spans[1:]:
                    prev = merged_spans[-1]
                    # 如果当前区间与前一个区间重叠，则合并
                    if current[0] <= prev[1]:
                        merged_spans[-1] = (prev[0], max(prev[1], current[1]))
                    else:
                        merged_spans.append(current)
                
                # 将当前行的区间与已有的区间进行全局合并
                # 如果这是第一行，直接添加
                if not all_columns:
                    all_columns.append(merged_spans)
                else:
                    # 获取已有的所有区间
                    existing_spans = [span for row in all_columns for span in row]
                    # 将当前行的区间与已有的区间合并
                    all_spans = existing_spans + merged_spans
                    all_spans.sort()
                    
                    # 全局合并区间
                    global_merged = []
                    if all_spans:
                        current_start, current_end = all_spans[0]
                        for start, end in all_spans[1:]:
                            if start <= current_end:
                                current_end = max(current_end, end)
                            else:
                                global_merged.append((current_start, current_end))
                                current_start, current_end = start, end
                        global_merged.append((current_start, current_end))
                    
                    # 清空原有的all_columns，并添加全局合并后的区间
                    all_columns = [global_merged]
        
        # 如果没有有效的行列数据，返回None
        if not all_columns:
            return None
            
        # 计算平均字符宽度
        min_gap_width = 0
        if char_count > 0:
            avg_char_width = avg_char_width / char_count
            # 设置最小间隙宽度为平均字符宽度
            min_gap_width = avg_char_width
            # print(f"Average character width: {avg_char_width:.2f}, minimum gap width: {min_gap_width:.2f}")
            
        # 找出所有行之间的共同空白区间
        common_gaps = None
        
        for column_spans in all_columns:
            # 计算当前行的空白区间
            row_gaps = []
            
            len_of_column_spans = len(column_spans)

            if len_of_column_spans == 0:
                continue

            # 列之间的空白
            for i in range(len_of_column_spans - 1):
                gap_start = column_spans[i][1]
                gap_end = column_spans[i+1][0]
                row_gaps.append((gap_start, gap_end))
            
            # 边缘加入计算
            # row_gaps.append((X0, column_spans[0][0]))
            # row_gaps.append((column_spans[len_of_column_spans - 1][1], X1))

        # 移出 row_gaps 中宽度小于 min_gap_width 的项
        row_gaps = [(start, end) for start, end in row_gaps if end - start >= min_gap_width]

        # 初始化或更新共同空白区间
        if common_gaps is None:
            common_gaps = row_gaps
        else:
            # 如果已经没有共同空白区间，直接返回None
            if not common_gaps:
                return None
                    
            # 找出当前行和之前行共同的空白区间
            new_common_gaps = []
            
            for gap1 in common_gaps:
                for gap2 in row_gaps:
                    # 如果两个空白区间有交集
                    start = max(gap1[0], gap2[0])
                    end = min(gap1[1], gap2[1])
                        
                    # 确保交集区域的宽度也大于最小间隙宽度
                    if end > start and (end - start) >= min_gap_width:
                        new_common_gaps.append((start, end))
                
            common_gaps = new_common_gaps
                
            # 如果已经没有共同空白区间，提前返回None
            if not common_gaps:
                return None
        
        # 传入min_rows=2，要求间隙至少能够将两个行进行分栏
        common_gaps = self.filter_gaps(common_gaps, rows, min_rows=1)

        if common_gaps:
            # 按照间隙宽度从大到小排序
            common_gaps.sort(key=lambda x: x[1] - x[0], reverse=True)
            # 返回空白区间列表
        return common_gaps

    def filter_gaps(self, gaps, rows, min_rows=1):
        """
        过滤空白间隙，只保留能够将至少N个row进行分栏且不会和rows中的任意Line重叠的间隙
        
        Args:
            gaps: 要过滤的空白间隙列表，每个间隙为(x0, x1)格式
            rows: 行列表，用于检查间隙是否会分割行内的元素
            min_rows: 间隙必须能够分栏的最少行数,默认为1
            
        Returns:
            list: 过滤后的空白间隙列表
        """
        if not gaps or not rows:
            return []
            
        # 存储有效的分栏空隙
        valid_gaps = []

        idx0, idx1 = (0, 2) if self.is_horizontal_write_mode_page() else (1, 3)
        
        for gap in gaps:
            gap_x0, gap_x1 = gap
            splits_any_line = False
            rows_split_count = 0  # 记录能够被该间隙分栏的行数
            
            # 检查每一行
            for row in rows:
                if not row:
                    continue
                    
                # 计算该行中空隙左右两侧的元素
                left_elements = []
                right_elements = []
                
                for element in row:
                    x0, x1 = element.bbox[idx0], element.bbox[idx1]
                    
                    # 检查空隙是否与元素有实质性重叠（重叠面积大于0）
                    # 允许空隙的边界恰好与Element的边界重合（重叠面积为0）
                    # 1. 空隙左边界穿过元素内部（不是边界）
                    # 2. 空隙右边界穿过元素内部（不是边界）
                    # 3. 元素完全位于空隙内部（且不与边界重合）
                    # 4. 空隙完全位于元素内部（且不与边界重合）
                    if (x0 < gap_x0 < x1) or (x0 < gap_x1 < x1) or \
                       (gap_x0 < x0 and x1 < gap_x1) or (x0 < gap_x0 and gap_x1 < x1):
                        splits_any_line = True
                        break
                        
                    # 元素在空隙左侧
                    if x1 <= gap_x0:
                        left_elements.append(element)
                    # 元素在空隙右侧
                    elif x0 >= gap_x1:
                        right_elements.append(element)
                
                # 如果空隙分割了元素，跳出当前行的检查
                if splits_any_line:
                    break
                    
                # 如果该行在空隙左右两侧都有元素，该行被成功分栏
                if left_elements and right_elements:
                    rows_split_count += 1
            
            # 如果空隙没有分割任何元素，并且能够将至少min_rows个行分栏，则添加到有效间隙列表
            if rows_split_count >= min_rows and not splits_any_line:
                valid_gaps.append(gap)
        
        return valid_gaps

    def __deal_gaps(self, gaps):
        '''
        处理空白间隙结果。对gaps依据value字段进行聚类，
        并返回聚类分好的组中value字段平均值最大的分组。
        
        Args:
            gaps: 包含(x1, x2, value)的列表，表示空白间隙的起始和结束坐标以及覆盖次数
            
        Returns:
            list: 处理后的gaps列表，为value平均值最大的聚类组
        '''
        if not gaps or len(gaps) < 2:
            return gaps
        
        import numpy as np
        import random
        
        # 提取value值作为聚类特征
        values = np.array([item[2] for item in gaps])
        values = values.reshape(-1, 1)  # 转换为二维数组，适合k-means算法
        
        # 确定聚类数量k，这里使用一个简单的启发式方法
        # 如果数据点少于10个，k=2，否则k=min(数据点数量的平方根向上取整, 5)
        k = min(max(2, int(np.ceil(np.sqrt(len(values))))), 5)
        
        def kmeans_plus_plus_init(data, k):
            # 随机选择第一个中心点
            centers = [data[random.randint(0, len(data)-1)]]
            
            # 选择剩余的k-1个中心点
            for _ in range(k-1):
                # 计算每个点到最近中心点的距离的平方
                dist_sq = np.array([min([np.sum((x-c)**2) for c in centers]) for x in data])
                
                # 防止除零风险
                sum_dist = dist_sq.sum()
                if sum_dist == 0:
                    # 如果所有距离都为0（所有点都与中心重合），使用均匀概率
                    probs = np.ones_like(dist_sq) / len(dist_sq)
                else:
                    # 计算概率分布
                    probs = dist_sq / sum_dist
                    
                # 基于概率选择下一个中心点
                cumulative_probs = probs.cumsum()
                r = random.random()
                for j, p in enumerate(cumulative_probs):
                    if r < p:
                        centers.append(data[j])
                        break
            
            return np.array(centers)
        
        # 初始化中心点
        centers = kmeans_plus_plus_init(values, k)
        
        # k-means聚类
        max_iterations = 100
        tolerance = 1e-4
        
        # 初始化聚类标签
        labels = np.zeros(len(values), dtype=int)
        
        for _ in range(max_iterations):
            # 分配每个点到最近的中心点
            new_labels = np.array([np.argmin([np.sum((x-c)**2) for c in centers]) for x in values])
            
            # 如果标签没有变化，则算法收敛
            if np.all(labels == new_labels):
                break
            
            labels = new_labels
            
            # 更新中心点
            new_centers = np.zeros_like(centers)
            for i in range(k):
                if np.sum(labels == i) > 0:  # 确保该类别有数据点
                    new_centers[i] = np.mean(values[labels == i], axis=0)
            
            # 检查收敛性
            center_shift = np.sum((centers - new_centers) ** 2)
            centers = new_centers
            
            if center_shift < tolerance:
                break
        
        # 计算每个聚类的平均value
        cluster_avg_values = []
        for i in range(k):
            cluster_indices = np.where(labels == i)[0]
            if len(cluster_indices) > 0:
                avg_value = np.mean([gaps[idx][2] for idx in cluster_indices])
                cluster_avg_values.append((i, avg_value))
        
        # 找出平均value最大的聚类
        if not cluster_avg_values:
            return gaps  # 如果聚类失败，返回原始结果
        
        best_cluster_id = max(cluster_avg_values, key=lambda x: x[1])[0]
        
        # 返回平均value最大的聚类中的所有元素
        best_cluster_indices = np.where(labels == best_cluster_id)[0]
        best_cluster_gaps = [gaps[i] for i in best_cluster_indices]
        
        return best_cluster_gaps

    def adjust_lines_bbox(self):
        elements = Collection()

        # 捕获所有的文本块信息
        elements.extend(self.blocks)
        
        # 捕获所有的形状信息
        elements.extend(self.shapes)

        # self.__dump_elements(elements)

        from pdf2docx.text.Line import Line
        import fitz

        # 捕获所有的Line对象
        # lines = []
        # for element in elements:
        #     if isinstance(element, Line):
        #         element._back_up_bbox = element.bbox
        #         lines.append(element)
        
        # # 检查并调整任意两个有直接交叉的Line的bbox
        # adjusted = True
        # while adjusted:
        #     adjusted = False
        #     for i in range(len(lines)):
        #         for j in range(i+1, len(lines)):
        #             line1 = lines[i]
        #             line2 = lines[j]
                    
        #             # 检查两个Line是否有直接交叉（水平和垂直方向上均有交叉）
        #             bbox1 = line1.bbox
        #             bbox2 = line2.bbox
                    
        #             # 计算交叉区域
        #             intersection = bbox1 & bbox2
                    
        #             # 如果交叉区域不为空且有面积（水平和垂直方向上均有交叉）
        #             if not intersection.is_empty and intersection.get_area() > 0:
        #                 # 找到交叉区域的中轴线
        #                 mid_y = (intersection.y0 + intersection.y1) / 2
                        
        #                 # 检查哪个bbox在上面，哪个在下面
        #                 if bbox1.y0 < bbox2.y0:  # bbox1在上面
        #                     new_bbox1 = fitz.Rect(bbox1.x0, bbox1.y0, bbox1.x1, mid_y - 1)
        #                     new_bbox2 = fitz.Rect(bbox2.x0, mid_y + 1, bbox2.x1, bbox2.y1)
        #                 else:  # bbox2在上面或两者起始位置相同
        #                     new_bbox1 = fitz.Rect(bbox1.x0, mid_y + 1, bbox1.x1, bbox1.y1)
        #                     new_bbox2 = fitz.Rect(bbox2.x0, bbox2.y0, bbox2.x1, mid_y - 1)
                        
        #                 # 更新Line的bbox
        #                 line1.bbox = new_bbox1
        #                 line2.bbox = new_bbox2
                        
        #                 # 标记已调整，继续检查直到没有交叉
        #                 adjusted = True
    
        return elements

    def remove_table_lines(self, connected_border_tolerance:float=0.5, min_border_clearance:float=2.0, max_border_width:float=6.0):
        '''
        将表格区域内的所有元素合并为一个大的Line元素，大小与表格相同。
        
        Args:
            connected_border_tolerance (float): 判断两个边框是否相交的容差值。
            min_border_clearance (float): 两个边框之间的最小允许间隙。
            max_border_width (float): 最大边框宽度。
        
        Returns:
            Collection: 处理后的元素集合。
        '''
        from pdf2docx.text.Line import Line
        from pdf2docx.text.TextSpan import TextSpan
        import fitz
        
        # 初始化结果集合
        elements = self.adjust_lines_bbox()
        
        # 如果没有元素，直接返回空集合
        if not elements:
            return elements
            
        # 如果没有形状，直接返回原始元素
        if not self.shapes: 
            return elements
        
        # 提取可能是表格的线条
        table_strokes = self.shapes.table_strokes
        if not table_strokes:
            return elements
        
        # 根据连通性将线条分组，每个组可能是一个潜在的表格
        grouped_strokes = table_strokes.group_by_connectivity(
            dx=connected_border_tolerance, 
            dy=connected_border_tolerance
        )
        
        # 如果没有有效的组，直接返回原始元素
        if not grouped_strokes:
            return elements
        
        # 创建一个新的Collection来存放处理后的元素
        from pdf2docx.common.Collection import Collection
        processed_elements = Collection()
        
        # 记录已处理的元素，避免重复添加
        processed_element_ids = set()
        
        # 对每个表格区域处理
        for strokes in grouped_strokes:
            # 标记表格区域内的元素为已处理
            for element in elements:
                # 检查元素是否完全在表格区域内
                # 元素的边界框必须完全在表格的边界框内
                if (strokes.bbox.x0 <= element.bbox.x0 and 
                    strokes.bbox.y0 <= element.bbox.y0 and 
                    strokes.bbox.x1 >= element.bbox.x1 and 
                    strokes.bbox.y1 >= element.bbox.y1):
                    # 标记为已处理
                    processed_element_ids.add(id(element))
            
            # 创建一个新的Line元素，大小与表格相同
            merged_line = Line()
            
            # 设置Line的属性
            merged_line.wmode = 0  # 水平文本模式
            merged_line.dir = [1.0, 0.0]  # 从左到右的方向
            
            # 使用表格的边界框作为Line的边界框
            merged_line.bbox = fitz.Rect(strokes.bbox.x0, strokes.bbox.y0, strokes.bbox.x1, strokes.bbox.y1)
            
            # 创建一个空的TextSpan，使用表格的边界框
            bbox_list = [strokes.bbox.x0, strokes.bbox.y0, strokes.bbox.x1, strokes.bbox.y1]
            empty_span = TextSpan({
                'bbox': bbox_list,
                'text': ' '  # 空格以保持表格的大小
            })
            
            # 添加空的TextSpan到Line
            merged_line.add_span(empty_span)
            
            # 将新创建的Line添加到结果中
            processed_elements.append(merged_line)
        
        # 添加不在任何表格区域内的元素
        for element in elements:
            # 如果这个元素还没有被处理过
            if id(element) not in processed_element_ids:
                processed_elements.append(element)

        # 过滤掉Fill元素，使其不参与布局计算
        from pdf2docx.shape.Shape import Fill
        from pdf2docx.common.Collection import Collection
        
        # 创建一个新的Collection对象
        filtered_elements = Collection()
        
        # 添加非Fill元素
        for element in processed_elements:
            if not (isinstance(element, Fill)):
                filtered_elements.append(element)
        
        return filtered_elements

    def __get_space_by_special_rows(self, rows, limit: int = 2):
        len_rows = len(rows)
        # __results是空白间隙结果列表，每项为(x0, x1, value, y_ranges)格式
        # x0, x1: 空白间隙的水平范围
        # value: 空白间隙的权重值
        # y_ranges: 空白间隙的垂直范围列表，每项为(y0, y1)格式
        __results = []  # List[Tuple[float, float, int, List[Tuple[float, float]]]]

        idx0, idx1 = (1, 3) if self.is_horizontal_write_mode_page() else (0, 2)

        def __get_merge_weight(line_count: int) -> int:
            return line_count * line_count

        def __get_rows_weight(rows) -> float:
            """
            计算所有行中所有文本行的高度总和，考虑重叠的情况
            例如：如果有三个文本行的垂直区域分别是(35,100,65,200),(35,300,65,500),(40,400,100,450)
            则总高度为300，因为第三个文本行与第二个文本行有重叠
            """
            if not rows:
                return 0.0

            # 收集所有行中的所有文本行的垂直范围
            all_line_ranges = []
            for row in rows:
                for line in row:
                    if hasattr(line, 'bbox'):
                        all_line_ranges.append((line.bbox[idx0], line.bbox[idx1]))  # (y0, y1)
            
            # 如果没有文本行，返回行的边界框高度
            if not all_line_ranges:
                return rows[-1].bbox[idx1] - rows[0].bbox[idx0]
            
            # 按照y0排序
            all_line_ranges.sort(key=lambda r: r[0])
            
            # 合并重叠的范围
            merged_ranges = [all_line_ranges[0]]
            for current_range in all_line_ranges[1:]:
                prev_range = merged_ranges[-1]
                
                # 如果当前范围与前一个范围重叠，合并它们
                if current_range[0] <= prev_range[1]:
                    # 更新前一个范围的y1为两个范围中的最大值
                    merged_ranges[-1] = (prev_range[0], max(prev_range[1], current_range[1]))
                else:
                    # 如果不重叠，添加新范围
                    merged_ranges.append(current_range)
            
            # 计算合并后的所有范围的高度总和
            total_height = sum(r[1] - r[0] for r in merged_ranges)
            
            return total_height

        if len_rows < limit: 
            # 将elements所有的数据组织为一个row
            # res = self.__calculate_space_by_special_rows(rows)
            # __results = self.__merge_results(__results, res, __get_merge_weight(len_rows))
            pass
        else:
            # 构造行
            for i in range(0, len_rows - limit + 1):
                for j in range(i + (limit - 1), len_rows):
                    # 从rows中提取出第i到j行，组成一个新的rows
                    new_rows = rows[i:j+1]
                    res = self.__calculate_space_by_special_rows(new_rows)
                    if res and len(res) > 2:
                        continue
                    # if res:
                    #     # 打印new_rows 中所有的行的文本信息
                    #     for row in new_rows:
                    #         for line in row:
                    #             print(line.text)
                    # 计算行高度差：最后一行底部 - 第一行顶部
                    row_height = __get_rows_weight(new_rows)
                    __results = self.__merge_results(__results, res, __get_merge_weight(row_height), new_rows[0].bbox[idx0], new_rows[-1].bbox[idx1])
            # X0, _, X1, _ = self.working_bbox
            # __results = [gap for gap in __results if gap[0] > X0 and gap[1] < X1]
        return __results

    def __dump_elements(self, elements):
        # 打印所有的element
        print('elements:', elements._instances)

    def __dump_gaps(self, gaps):
        # 打印所有的gaps
        print('gaps:', gaps)

    def __dump_sections(self, sections):
        # 打印所有的sections
        for idx, section in enumerate(sections):
            print(f'section {idx}:', section.bbox, section._instances)

    def parse_section_ex(self, **settings):
        '''Detect and create page sections.'''
        # collect all blocks (line level) and shapes
        elements = self.remove_table_lines()
        if not elements: return None

        def get_images():
            """
            获取不跨越竖直中轴线的图片元素
            
            中轴线定义为页面宽度的中点
            
            Returns:
                list: 不跨越竖直中轴线的图片元素列表
            """
            idx0, idx1 = (0,2) if self.is_horizontal_write_mode_page() else (1,3)
            idy0, idy1 = (1,3) if self.is_horizontal_write_mode_page() else (0,2)

            # 获取页面的宽度
            page_width = self.bbox[idx1]
            
            # 计算页面的竖直中轴线
            middle_x = page_width / 2.0
            
            # 筛选不跨越竖直中轴线的图片
            non_crossing_images = []
            
            # 检查浮动图片
            for img in self.blocks.floating_image_blocks:
                x0, x1 = img.bbox[idx0], img.bbox[idx1]
                # 如果图片不跨越中轴线（全部在左侧或全部在右侧）
                if (x0 >= middle_x) or (x1 <= middle_x):
                    non_crossing_images.append(img)
            
            # 检查内联图片
            for img in self.blocks.inline_image_blocks:
                x0, x1 = img.bbox[idx0], img.bbox[idx1]
                # 如果图片不跨越中轴线（全部在左侧或全部在右侧）
                if (x0 >= middle_x) or (x1 <= middle_x):
                    non_crossing_images.append(img)
                    
            return non_crossing_images

        # elements.extend(get_images())

        __gaps = []

        def get_factors(elements):
            # 默认情况
            return [0.00]

        # 计算水平重叠占比作为分组因子
        factors = get_factors(elements)
        # 使用计算出的因子进行行分组并计算空白间隙
        for factor in factors:
            __gaps = self.__get_space_by_special_rows(elements.group_by_rows(factor=factor, text_direction=self.is_vertical_write_mode_page()), limit=1)
            if __gaps:
                break

        def merge_gaps_crossed():
            # 完全重新实现，将所有相交的间隙合并为一个间隙
            
            # 如果结果为空或只有一个间隙，直接返回
            if not __gaps or len(__gaps) == 1:
                return __gaps
                
            # 初始化用于标记已处理间隙的集合
            processed = set()
            final_gaps = []
            
            # 遍历所有间隙
            for i, gap1 in enumerate(__gaps):
                # 如果该间隙已被处理，跳过
                if i in processed:
                    continue
                    
                # 标记当前间隙为已处理
                processed.add(i)
                
                # 初始化合并后的间隙
                merged_gap = gap1.copy()  # 复制当前间隙作为初始合并间隙
                merged_value = merged_gap[2]  # 初始权重值
                
                # 初始化合并后的垂直范围列表
                merged_ranges = []
                if len(merged_gap) > 3 and merged_gap[3]:
                    merged_ranges.extend(merged_gap[3])
                
                # 标记是否有新的合并发生
                merged_occurred = True
                
                # 循环直到没有新的合并发生
                while merged_occurred:
                    merged_occurred = False
                    
                    # 检查其他所有间隙
                    for j, gap2 in enumerate(__gaps):
                        # 跳过已处理的间隙
                        if j in processed:
                            continue
                            
                        # 检查是否有交叉（两种情况：gap2的起点在gap1内部，或gap1的起点在gap2内部）
                        if (merged_gap[0] <= gap2[0] <= merged_gap[1]) or (gap2[0] <= merged_gap[0] <= gap2[1]):
                            # 合并间隙范围：取交集区域
                            new_x0 = max(merged_gap[0], gap2[0])
                            new_x1 = min(merged_gap[1], gap2[1])
                            
                            # 只有当交集有效（起点小于终点）时才进行合并
                            if new_x0 < new_x1:
                                # 更新合并后的间隙范围
                                merged_gap[0] = new_x0
                                merged_gap[1] = new_x1
                                
                                # 累加权重值
                                merged_value += gap2[2]
                                
                                # 合并垂直范围
                                if len(gap2) > 3 and gap2[3]:
                                    merged_ranges.extend(gap2[3])
                                
                                # 标记该间隙为已处理
                                processed.add(j)
                                
                                # 标记有新的合并发生
                                merged_occurred = True
                
                # 合并重叠的垂直范围
                if merged_ranges:
                    # 对范围按起始位置排序
                    ranges = sorted(merged_ranges, key=lambda x: x[0])
                    
                    # 合并重叠的范围
                    final_ranges = []
                    if ranges:
                        current_range = ranges[0]
                        
                        for r in ranges[1:]:
                            # 如果当前范围与累积范围重叠或相邻
                            if r[0] <= current_range[1]:
                                # 扩展当前范围
                                current_range = (current_range[0], max(current_range[1], r[1]))
                            else:
                                # 添加当前范围到结果并开始新范围
                                final_ranges.append(current_range)
                                current_range = r
                        
                        # 添加最后一个范围
                        final_ranges.append(current_range)
                
                # 更新合并后的间隙
                merged_gap[2] = merged_value
                merged_gap[3] = final_ranges
                
                # 添加到最终结果
                final_gaps.append(merged_gap)
            
            return final_gaps

        # 合并交叉gap为一个gap，取其交叉区域为gap的区域
        if __gaps:
            __gaps = merge_gaps_crossed()

        if __gaps:
            # avg_value = sum([gap[2] for gap in __results]) / len(__results)
            max_avg_value = max([gap[2] for gap in __gaps])
            # __results = self.deal_space_results(__results)
            # __results = [gap for gap in __results if gap[2] >= avg_value]
            if len(__gaps) > 2:
                __gaps = self.__deal_gaps(__gaps)
                __gaps = [gap for gap in __gaps if gap[2] >= (max_avg_value / 2)]
            else:
                # 对 avg_value 开根号
                # avg_value = math.sqrt(avg_value)
                __gaps = [gap for gap in __gaps if gap[2] >= (max_avg_value / 2)]

        # create sections
        sections = self.__create_sections_ex(gaps=__gaps)
        self.gaps = __gaps

        # 打印所有的gaps
        # self.__dump_gaps(__gaps)

        # 打印所有的sections
        # self.__dump_sections(sections)

        return sections

    def parse_section(self, **settings):
        '''Detect and create page sections.

        .. note::
            - Only two-columns Sections are considered for now.
            - Page margin must be parsed before this step.
        '''
        # bbox
        X0, Y0, X1, _ = self.working_bbox # 页面布局的尺寸信息

        # collect all blocks (line level) and shapes
        elements = Collection()
        elements.extend(self.blocks) # 捕获所有的文本块信息

        # 暂时不考虑形状(形状参与布局会打乱原来的段落)
        elements.extend(self.shapes) # 捕获所有的形状信息
        if not elements: return

        # to create section with collected lines
        lines = Collection()
        sections = []
        def close_section(num_col, elements, y_ref):
            # append to last section if both single column
            if sections and sections[-1].num_cols==num_col==1:
                column = sections[-1][0] # type: Column
                column.union_bbox(elements)
                column.add_elements(elements)
            # otherwise, create new section
            else:
                section = self._create_section(num_col, elements, (X0, X1), y_ref)
                if section:
                    sections.append(section)


        # check section row by row
        pre_num_col = 1
        y_ref = Y0 # to calculate v-distance between sections
        rows = elements.group_by_rows()
        for row in rows: # 所有元素按照行的连通性分行组
            # check column col by col
            cols = row.group_by_columns() # 同理根据分好行的组，再根据每一行组，再将改行组内的元素根据列的连通性分组
            current_num_col = len(cols)

            # column check:
            # consider 2-cols only
            if current_num_col>2:
                current_num_col = 1

            # the width of two columns shouldn't have significant difference
            elif current_num_col==2: # 如果该行分组是两列的情况，就根据左右列宽占比情况来判断是否为两列
                u0, v0, u1, v1 = cols[0].bbox
                m0, n0, m1, n1 = cols[1].bbox
                x0 = (u1+m0)/2.0
                c1, c2 = x0-X0, X1-x0 # column width ,这里是根据计算出来中轴线得出的理论列宽
                w1, w2 = u1-u0, m1-m0 # line width, 这里是实际的列宽
                f = 2.0
                if not 1/f<=c1/c2<=f or w1/c1<0.33 or w2/c2<0.33: # 如果实际占比小于理论的1/3,认为这不是两列, 或者分出的两列宽度占比不合理，也认为不是两列
                    current_num_col = 1

            # process exceptions
            if pre_num_col==2 and current_num_col==1:
                # though current row has one single column, it might have another virtual
                # and empty column. If so, it should be counted as 2-cols
                cols = lines.group_by_columns() # 计算出最新的分列数据
                if row.bbox[2]<=cols[1].bbox[0] and row.bbox[0]>=cols[0].bbox[2]: # 恰好中间部分，单独一算一个节吧
                    current_num_col = 1
                elif row.bbox[2]<=cols[1].bbox[0] or row.bbox[0]>=cols[0].bbox[2]: # 如果当前仅一行的列, 并且新列仅在旧列的某一侧出现，则认为依然是两列的布局
                    combine = Collection(lines)
                    combine.extend(row)
                    if len(combine.group_by_columns(sorted=False))==2: 
                        current_num_col = 2
                    else:
                        current_num_col = 1
                # pre_num_col!=current_num_col => to close section with collected lines,
                # before that, further check the height of collected lines
                else:
                    x0, y0, x1, y1 = lines.bbox
                    if y1-y0<settings['min_section_height']:
                        pre_num_col = 1


            elif pre_num_col==2 and current_num_col==2: # 如果当前行列数与前列相同,则与上述行合并，并计算新的分列情况，如果是1列，则当前列也认为是同一列
                # though both 2-cols, they don't align with each other
                combine = Collection(lines)
                combine.extend(row)
                if len(combine.group_by_columns(sorted=False))!=2: 
                    current_num_col = 1


            # finalize pre-section if different from the column count of previous section
            if current_num_col!=pre_num_col: # 如果当前列数和前一列数不同，则新起一节
                # process pre-section
                close_section(pre_num_col, lines, y_ref)
                if sections:
                    y_ref = sections[-1][-1].bbox[3]

                # start potential new section
                lines = Collection(row)
                pre_num_col = current_num_col

            # otherwise, collect current lines for further processing
            else:
                lines.extend(row)

        # don't forget the final section
        close_section(current_num_col, lines, y_ref)

        return sections


    @staticmethod
    def _create_section(num_col:int, elements:Collection, h_range:tuple, y_ref:float):
        '''Create section based on column count, candidate elements and horizontal boundary.'''
        if not elements: return
        X0, X1 = h_range

        if num_col==1:
            x0, y0, x1, y1 = elements.bbox
            # Note: do not use Column((X0, y0, X1, y1)) directly here. We have to set final bbox
            # per update_bbox to avoid double rotation in case page rotation exists.
            column = Column().update_bbox((X0, y0, X1, y1)) # this is final bbox, must use update_bbox
            column.add_elements(elements)
            section = Section(space=0, columns=[column])
            before_space = y0 - y_ref
        else:
            cols = elements.group_by_columns()
            u0, v0, u1, v1 = cols[0].bbox
            m0, n0, m1, n1 = cols[1].bbox
            u = (u1+m0)/2.0

            column_1 = Column().update_bbox((X0, v0, u, v1))
            column_1.add_elements(elements)

            column_2 = Column().update_bbox((u, n0, X1, n1))
            column_2.add_elements(elements)

            section = Section(space=0, columns=[column_1, column_2])
            before_space = v0 - y_ref

        section.before_space = round(before_space, 1)
        return section
