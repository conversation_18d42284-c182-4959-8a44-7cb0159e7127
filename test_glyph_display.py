#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字形边界框显示功能
"""

import tkinter as tk
from pdf2docx.gui.MainFrame import MainFrame
import sys
import os

def test_glyph_display():
    """测试字形边界框显示功能"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("PDF2DOCX GUI - 字形边界框测试")
    root.geometry("800x600")
    
    # 创建MainFrame
    main_frame = MainFrame(root)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    print("GUI已启动，请执行以下步骤测试字形边界框功能：")
    print("1. 点击'选择PDF文件'按钮")
    print("2. 选择一个PDF文件（建议使用test/samples/demo-text.pdf）")
    print("3. 点击'预览'按钮打开预览窗口")
    print("4. 在预览窗口中，勾选'显示字形边界'复选框")
    print("5. 观察字符是否显示橙色的字形边界框")
    print("6. 可以同时勾选'显示字符边界'来对比字符边界框（蓝色）和字形边界框（橙色）")
    print("\n预期结果：")
    print("- 字形边界框（橙色）应该比字符边界框（蓝色）稍小")
    print("- 字形边界框应该更紧密地包围字符的实际形状")
    
    # 启动GUI事件循环
    root.mainloop()

if __name__ == "__main__":
    test_glyph_display()
